const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin', 'client'],
    default: 'client'
  },
  // Client-specific fields
  companyName: {
    type: String,
    required: function() { return this.role === 'client'; }
  },
  contactName: {
    type: String,
    required: function() { return this.role === 'client'; }
  },
  phone: {
    type: String
  },
  website: {
    type: String
  },
  industry: {
    type: String
  },
  // Product preferences
  productType: {
    type: String,
    enum: ['watches', 'bracelets', 'both'],
    default: 'watches'
  },
  // Subscription and billing
  subscriptionPlan: {
    type: String,
    enum: ['basic', 'premium', 'enterprise'],
    default: 'basic'
  },
  subscriptionStatus: {
    type: String,
    enum: ['active', 'inactive', 'trial', 'suspended'],
    default: 'trial'
  },
  trialEndsAt: {
    type: Date
  },
  // Settings
  settings: {
    notifications: {
      email: { type: <PERSON><PERSON>an, default: true },
      sms: { type: Boolean, default: false }
    },
    analytics: {
      dataRetention: { type: Number, default: 365 }, // days
      shareData: { type: Boolean, default: false }
    }
  },
  // Metadata
  lastLoginAt: {
    type: Date
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update updatedAt on save
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to get public profile
userSchema.methods.getPublicProfile = function() {
  const user = this.toObject();
  delete user.password;
  return user;
};

// Static method to find clients
userSchema.statics.findClients = function() {
  return this.find({ role: 'client' }).select('-password');
};

module.exports = mongoose.model('User', userSchema);