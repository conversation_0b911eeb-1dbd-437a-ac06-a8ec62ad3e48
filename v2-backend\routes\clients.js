const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const TryOnSession = require('../models/TryOnSession');
const Product = require('../models/Product');

// Middleware to verify JWT token
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ message: 'No authentication token, access denied' });
    }

    const jwt = require('jsonwebtoken');
    const verified = jwt.verify(token, process.env.JWT_SECRET);
    req.user = verified;
    next();
  } catch (err) {
    res.status(401).json({ message: 'Token verification failed, authorization denied' });
  }
};

// Middleware to check admin role
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }
  next();
};

// GET /api/clients - Get all clients with analytics
router.get('/', auth, requireAdmin, async (req, res) => {
  try {
    const { search, status, page = 1, limit = 10 } = req.query;
    
    // Build query
    let query = { role: 'client' };
    
    if (search) {
      query.$or = [
        { companyName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { contactName: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status && status !== 'all') {
      query.subscriptionStatus = status;
    }

    // Get clients with pagination
    const clients = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get analytics for each client
    const clientsWithAnalytics = await Promise.all(
      clients.map(async (client) => {
        const clientObj = client.toObject();
        
        // Get try-on sessions count
        const totalSessions = await TryOnSession.countDocuments({ clientId: client._id });
        const conversions = await TryOnSession.countDocuments({ 
          clientId: client._id, 
          converted: true 
        });
        
        // Get revenue (sum of converted sessions)
        const revenueData = await TryOnSession.aggregate([
          { $match: { clientId: client._id, converted: true } },
          { $group: { _id: null, total: { $sum: '$orderValue' } } }
        ]);
        
        // Get product count
        const productCount = await Product.countDocuments({ clientId: client._id });
        
        // Calculate last active
        const lastSession = await TryOnSession.findOne({ clientId: client._id })
          .sort({ createdAt: -1 });
        
        return {
          ...clientObj,
          analytics: {
            totalSessions,
            conversions,
            conversionRate: totalSessions > 0 ? ((conversions / totalSessions) * 100).toFixed(1) : '0.0',
            revenue: revenueData[0]?.total || 0,
            productCount,
            lastActive: lastSession?.createdAt || client.lastLoginAt || client.createdAt
          }
        };
      })
    );

    // Get total count for pagination
    const totalClients = await User.countDocuments(query);

    res.json({
      clients: clientsWithAnalytics,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalClients / limit),
        totalClients,
        hasNext: page * limit < totalClients,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Get clients error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/clients/:id - Get specific client
router.get('/:id', auth, requireAdmin, async (req, res) => {
  try {
    const client = await User.findById(req.params.id).select('-password');
    
    if (!client || client.role !== 'client') {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Get detailed analytics
    const analytics = await TryOnSession.getAnalytics(client._id);
    
    res.json({
      ...client.toObject(),
      analytics
    });
  } catch (error) {
    console.error('Get client error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// POST /api/clients - Create new client
router.post('/', auth, requireAdmin, [
  body('email').isEmail().withMessage('Please enter a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('companyName').notEmpty().withMessage('Company name is required'),
  body('contactName').notEmpty().withMessage('Contact name is required'),
  body('website').optional().isURL().withMessage('Please enter a valid website URL'),
  body('phone').optional().trim(),
  body('industry').optional().trim(),
  body('productType').optional().isIn(['watches', 'bracelets', 'both']).withMessage('Invalid product type'),
  body('subscriptionPlan').optional().isIn(['basic', 'premium', 'enterprise']).withMessage('Invalid subscription plan')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      email,
      password,
      companyName,
      contactName,
      website,
      phone,
      industry,
      productType = 'watches',
      subscriptionPlan = 'basic'
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // Create new client
    const client = new User({
      email,
      password,
      role: 'client',
      companyName,
      contactName,
      website,
      phone,
      industry,
      productType,
      subscriptionPlan,
      subscriptionStatus: 'trial',
      trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days trial
    });

    await client.save();

    // Return client without password
    const clientResponse = client.toObject();
    delete clientResponse.password;

    res.status(201).json({
      message: 'Client created successfully',
      client: clientResponse
    });
  } catch (error) {
    console.error('Create client error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/clients/:id - Update client
router.put('/:id', auth, requireAdmin, [
  body('email').optional().isEmail().withMessage('Please enter a valid email'),
  body('companyName').optional().notEmpty().withMessage('Company name cannot be empty'),
  body('contactName').optional().notEmpty().withMessage('Contact name cannot be empty'),
  body('website').optional().isURL().withMessage('Please enter a valid website URL'),
  body('phone').optional().trim(),
  body('industry').optional().trim(),
  body('productType').optional().isIn(['watches', 'bracelets', 'both']).withMessage('Invalid product type'),
  body('subscriptionPlan').optional().isIn(['basic', 'premium', 'enterprise']).withMessage('Invalid subscription plan'),
  body('subscriptionStatus').optional().isIn(['active', 'inactive', 'trial', 'suspended']).withMessage('Invalid subscription status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const clientId = req.params.id;
    const updateData = req.body;

    // Remove password from update data if present (use separate endpoint for password changes)
    delete updateData.password;
    delete updateData.role; // Prevent role changes

    // Check if email is being changed and if it already exists
    if (updateData.email) {
      const existingUser = await User.findOne({
        email: updateData.email,
        _id: { $ne: clientId }
      });
      if (existingUser) {
        return res.status(400).json({ message: 'Email already exists' });
      }
    }

    const client = await User.findOneAndUpdate(
      { _id: clientId, role: 'client' },
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!client) {
      return res.status(404).json({ message: 'Client not found' });
    }

    res.json({
      message: 'Client updated successfully',
      client
    });
  } catch (error) {
    console.error('Update client error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/clients/:id - Delete client
router.delete('/:id', auth, requireAdmin, async (req, res) => {
  try {
    const clientId = req.params.id;

    // Check if client exists
    const client = await User.findOne({ _id: clientId, role: 'client' });
    if (!client) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Soft delete by setting isActive to false
    await User.findByIdAndUpdate(clientId, {
      isActive: false,
      subscriptionStatus: 'inactive'
    });

    // Optionally, you might want to also deactivate their products
    await Product.updateMany(
      { clientId },
      { status: 'inactive' }
    );

    res.json({ message: 'Client deleted successfully' });
  } catch (error) {
    console.error('Delete client error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/clients/:id/password - Change client password
router.put('/:id/password', auth, requireAdmin, [
  body('newPassword').isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const clientId = req.params.id;
    const { newPassword } = req.body;

    const client = await User.findOne({ _id: clientId, role: 'client' });
    if (!client) {
      return res.status(404).json({ message: 'Client not found' });
    }

    client.password = newPassword;
    await client.save();

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/clients/stats - Get client statistics for admin dashboard
router.get('/stats/overview', auth, requireAdmin, async (req, res) => {
  try {
    const totalClients = await User.countDocuments({ role: 'client' });
    const activeClients = await User.countDocuments({
      role: 'client',
      subscriptionStatus: 'active'
    });
    const trialClients = await User.countDocuments({
      role: 'client',
      subscriptionStatus: 'trial'
    });
    const recentClients = await User.countDocuments({
      role: 'client',
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    });

    // Get subscription plan distribution
    const planDistribution = await User.aggregate([
      { $match: { role: 'client' } },
      { $group: { _id: '$subscriptionPlan', count: { $sum: 1 } } }
    ]);

    res.json({
      totalClients,
      activeClients,
      trialClients,
      recentClients,
      planDistribution
    });
  } catch (error) {
    console.error('Client stats error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
