const { db } = require('./config/firebase');
const User = require('./models/User');
const TryOnSession = require('./models/TryOnSession');
require('dotenv').config();

const seedData = async () => {
  try {
    console.log('Starting to seed Firestore database...');

    // Clear existing data
    const usersSnapshot = await db.collection('users').get();
    const sessionsSnapshot = await db.collection('tryOnSessions').get();
    
    const batch = db.batch();
    
    usersSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    sessionsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log('Cleared existing data');

    // Create admin user
    const adminUser = new User({
      email: '<EMAIL>',
      password: 'ViaAdmin2024!@#$',
      role: 'admin'
    });

    await adminUser.save();
    console.log('Admin user created');

    // Create client users with strong passwords and comprehensive test data
    const clientUsers = [
      {
        email: '<EMAIL>',
        password: 'LuxuryWatch2024!@#',
        role: 'client',
        companyName: 'Luxury Watches Co.',
        contactName: 'John Smith',
        phone: '******-0101',
        website: 'https://luxurywatches.com',
        industry: 'Luxury Goods',
        productType: 'watches',
        subscriptionPlan: 'premium',
        subscriptionStatus: 'active',
        trialEndsAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
      },
      {
        email: '<EMAIL>',
        password: 'ModernBracelet2024!@#',
        role: 'client',
        companyName: 'Modern Bracelets',
        contactName: 'Sarah Johnson',
        phone: '******-0102',
        website: 'https://modernbracelets.com',
        industry: 'Jewelry',
        productType: 'bracelets',
        subscriptionPlan: 'basic',
        subscriptionStatus: 'trial',
        trialEndsAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      },
      {
        email: '<EMAIL>',
        password: 'PremiumAccess2024!@#',
        role: 'client',
        companyName: 'Premium Accessories',
        contactName: 'Mike Davis',
        phone: '******-0103',
        website: 'https://premiumaccessories.com',
        industry: 'Fashion',
        productType: 'both',
        subscriptionPlan: 'enterprise',
        subscriptionStatus: 'active',
        trialEndsAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
      },
      {
        email: '<EMAIL>',
        password: 'WatchStore2024!@#',
        role: 'client',
        companyName: 'Elite Watch Store',
        contactName: 'Emma Wilson',
        phone: '******-0104',
        website: 'https://watchstore.com',
        industry: 'Retail',
        productType: 'watches',
        subscriptionPlan: 'basic',
        subscriptionStatus: 'active',
        trialEndsAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // 90 days from now
      },
      {
        email: '<EMAIL>',
        password: 'JewelryPlus2024!@#',
        role: 'client',
        companyName: 'Jewelry Plus',
        contactName: 'David Chen',
        phone: '******-0105',
        website: 'https://jewelryplus.com',
        industry: 'Jewelry',
        productType: 'bracelets',
        subscriptionPlan: 'premium',
        subscriptionStatus: 'active',
        trialEndsAt: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) // 180 days from now
      }
    ];

    const createdClients = [];
    for (const clientData of clientUsers) {
      const client = new User(clientData);
      await client.save();
      createdClients.push(client);
      console.log(`Client created: ${client.email}`);
    }

    // Create sample try-on sessions for analytics testing
    const sampleSessions = [];
    const productCategories = ['watches', 'bracelets'];
    const deviceTypes = ['mobile', 'tablet', 'desktop'];
    const countries = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'JP'];
    
    for (let i = 0; i < 50; i++) {
      const randomClient = createdClients[Math.floor(Math.random() * createdClients.length)];
      const randomCategory = productCategories[Math.floor(Math.random() * productCategories.length)];
      const randomDevice = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
      const randomCountry = countries[Math.floor(Math.random() * countries.length)];
      
      // Create sessions from the last 30 days
      const randomDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      
      const session = new TryOnSession({
        clientId: randomClient.id,
        sessionId: `session_${Date.now()}_${i}`,
        productId: `product_${randomCategory}_${i}`,
        productName: `Sample ${randomCategory.slice(0, -1)} ${i + 1}`,
        productCategory: randomCategory,
        productPrice: Math.floor(Math.random() * 1000) + 100,
        duration: Math.floor(Math.random() * 300) + 30, // 30-330 seconds
        interactions: [
          {
            type: 'rotation',
            timestamp: randomDate,
            data: { angle: Math.floor(Math.random() * 360) }
          },
          {
            type: 'size_adjustment',
            timestamp: new Date(randomDate.getTime() + 10000),
            data: { size: Math.floor(Math.random() * 20) + 35 }
          }
        ],
        device: {
          type: randomDevice,
          os: randomDevice === 'mobile' ? 'iOS' : 'Windows',
          browser: 'Chrome',
          screenResolution: randomDevice === 'mobile' ? '375x667' : '1920x1080'
        },
        location: {
          country: randomCountry,
          region: 'Test Region',
          city: 'Test City',
          timezone: 'UTC'
        },
        converted: Math.random() > 0.7, // 30% conversion rate
        conversionValue: Math.random() > 0.7 ? Math.floor(Math.random() * 500) + 100 : null,
        referrer: {
          source: 'google',
          medium: 'organic',
          campaign: 'test_campaign'
        },
        startTime: randomDate,
        endTime: new Date(randomDate.getTime() + (Math.floor(Math.random() * 300) + 30) * 1000),
        createdAt: randomDate
      });
      
      await session.save();
      sampleSessions.push(session);
    }
    
    console.log(`Created ${sampleSessions.length} sample try-on sessions`);

    console.log('\n=== SEED DATA SUMMARY ===');
    console.log('Admin User:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: ViaAdmin2024!@#$');
    console.log('\nClient Users:');
    createdClients.forEach(client => {
      console.log(`  ${client.email} (${client.productType}) - Password: ${clientUsers.find(u => u.email === client.email)?.password}`);
    });
    console.log(`\nTotal Sessions Created: ${sampleSessions.length}`);
    console.log('\nDatabase seeded successfully!');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    process.exit(0);
  }
};

// Run the seed function
seedData();
