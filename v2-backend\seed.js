const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('./models/User');
const TryOnSession = require('./models/TryOnSession');
const Product = require('./models/Product');

dotenv.config();

const seedDatabase = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Clear existing data
    await User.deleteMany({});
    await TryOnSession.deleteMany({});
    await Product.deleteMany({});
    console.log('Cleared existing data');

    // Create admin user
    const admin = new User({
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin'
    });

    await admin.save();
    console.log('Admin user created');

    // Create client users with enhanced data
    const clientUsers = [
      {
        email: '<EMAIL>',
        password: 'client123',
        role: 'client',
        companyName: 'Luxury Watches Co.',
        contactName: '<PERSON>',
        phone: '******-0101',
        website: 'https://luxurywatches.com',
        industry: 'Luxury Goods',
        productType: 'watches',
        subscriptionPlan: 'premium',
        subscriptionStatus: 'active'
      },
      {
        email: '<EMAIL>',
        password: 'client123',
        role: 'client',
        companyName: 'Elegant Bracelets',
        contactName: 'Sarah Johnson',
        phone: '******-0102',
        website: 'https://elegantbracelets.com',
        industry: 'Jewelry',
        productType: 'bracelets',
        subscriptionPlan: 'basic',
        subscriptionStatus: 'active'
      },
      {
        email: '<EMAIL>',
        password: 'client123',
        role: 'client',
        companyName: 'Premium Accessories',
        contactName: 'Mike Davis',
        phone: '******-0103',
        website: 'https://premiumaccessories.com',
        industry: 'Fashion',
        productType: 'both',
        subscriptionPlan: 'enterprise',
        subscriptionStatus: 'active'
      }
    ];

    const savedClients = [];
    for (const userData of clientUsers) {
      const user = new User(userData);
      await user.save();
      savedClients.push(user);
      console.log(`Client user created: ${userData.email}`);
    }

    console.log('Database seeding completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedDatabase();