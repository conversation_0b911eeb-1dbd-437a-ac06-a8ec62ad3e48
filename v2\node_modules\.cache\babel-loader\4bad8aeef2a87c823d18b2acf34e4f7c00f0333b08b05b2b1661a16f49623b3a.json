{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\admin\\\\Clients.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\nconst Clients = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    revenueGrowth: 0\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to fetch clients');\n      }\n      const data = await response.json();\n      setClients(data.clients || []);\n\n      // Calculate stats\n      const now = new Date();\n      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n\n      // Calculate new clients this month\n      const newClientsThisMonth = data.clients.filter(client => new Date(client.createdAt) >= firstDayOfMonth).length;\n\n      // Calculate active rate\n      const activeClients = data.clients.filter(client => client.subscriptionStatus === 'active').length;\n      const activeRate = data.clients.length > 0 ? activeClients / data.clients.length * 100 : 0;\n\n      // Calculate try-ons growth (comparing current month to previous month)\n      const currentMonthTryOns = data.clients.reduce((sum, client) => {\n        var _client$analytics;\n        const clientTryOns = ((_client$analytics = client.analytics) === null || _client$analytics === void 0 ? void 0 : _client$analytics.totalSessions) || 0;\n        return sum + (new Date(client.createdAt) >= firstDayOfMonth ? clientTryOns : 0);\n      }, 0);\n      const lastMonthTryOns = data.clients.reduce((sum, client) => {\n        var _client$analytics2;\n        const clientTryOns = ((_client$analytics2 = client.analytics) === null || _client$analytics2 === void 0 ? void 0 : _client$analytics2.totalSessions) || 0;\n        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);\n        return sum + (new Date(client.createdAt) >= lastMonth && new Date(client.createdAt) <= lastMonthEnd ? clientTryOns : 0);\n      }, 0);\n      const tryOnsGrowth = lastMonthTryOns > 0 ? (currentMonthTryOns - lastMonthTryOns) / lastMonthTryOns * 100 : 0;\n\n      // Calculate revenue growth\n      const currentMonthRevenue = data.clients.reduce((sum, client) => {\n        var _client$analytics3;\n        const clientRevenue = ((_client$analytics3 = client.analytics) === null || _client$analytics3 === void 0 ? void 0 : _client$analytics3.revenue) || 0;\n        return sum + (new Date(client.createdAt) >= firstDayOfMonth ? clientRevenue : 0);\n      }, 0);\n      const lastMonthRevenue = data.clients.reduce((sum, client) => {\n        var _client$analytics4;\n        const clientRevenue = ((_client$analytics4 = client.analytics) === null || _client$analytics4 === void 0 ? void 0 : _client$analytics4.revenue) || 0;\n        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);\n        return sum + (new Date(client.createdAt) >= lastMonth && new Date(client.createdAt) <= lastMonthEnd ? clientRevenue : 0);\n      }, 0);\n      const revenueGrowth = lastMonthRevenue > 0 ? (currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100 : 0;\n      setStats({\n        newClientsThisMonth,\n        activeRate,\n        tryOnsGrowth,\n        revenueGrowth\n      });\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      // Use fallback data for development\n      setClients([{\n        _id: '1',\n        companyName: 'Luxury Watches Co.',\n        website: 'luxurywatches.com',\n        email: '<EMAIL>',\n        subscriptionStatus: 'active',\n        createdAt: '2024-01-15',\n        analytics: {\n          totalSessions: 3420,\n          conversionRate: '22.4',\n          revenue: 45600,\n          productCount: 24,\n          lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n        },\n        subscriptionPlan: 'premium'\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = date => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setClientForm(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({\n      ...prev,\n      password: generatePassword()\n    }));\n  };\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n  const handleAddClient = async e => {\n    e.preventDefault();\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n      const data = await response.json();\n      setShowModal(false);\n      resetForm();\n      fetchClients(); // Refresh the list\n      alert('Client created successfully!');\n    } catch (err) {\n      console.error('Error creating client:', err);\n      alert(`Error: ${err.message}`);\n    }\n  };\n  const handleEditClient = async e => {\n    e.preventDefault();\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n      setShowModal(false);\n      resetForm();\n      fetchClients(); // Refresh the list\n      alert('Client updated successfully!');\n    } catch (err) {\n      console.error('Error updating client:', err);\n      alert(`Error: ${err.message}`);\n    }\n  };\n  const handleDeleteClient = async clientId => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n      fetchClients(); // Refresh the list\n      alert('Client deleted successfully!');\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      alert(`Error: ${err.message}`);\n    }\n  };\n  const openEditModal = client => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '',\n      // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdminNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-16 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Client Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage your virtual try-on clients and track their performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n            onClick: openAddModal,\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), \"Add Client\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [\"+\", stats.newClientsThisMonth, \" new\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Active Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-green-600\",\n                children: [stats.activeRate.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"active rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics;\n                    return sum + (((_c$analytics = c.analytics) === null || _c$analytics === void 0 ? void 0 : _c$analytics.totalSessions) || 0);\n                  }, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.tryOnsGrowth >= 0 ? '+' : '', stats.tryOnsGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [\"$\", loading ? '...' : clients.reduce((sum, c) => {\n                    var _c$analytics2;\n                    return sum + (((_c$analytics2 = c.analytics) === null || _c$analytics2 === void 0 ? void 0 : _c$analytics2.revenue) || 0);\n                  }, 0).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Globe, {\n                  className: \"h-6 w-6 text-orange-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${stats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [stats.revenueGrowth >= 0 ? '+' : '', stats.revenueGrowth.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"this month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute top-3 right-3 text-gray-400 hover:text-gray-600\",\n              onClick: () => setShowModal(false),\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: editingClient ? 'Edit Client' : 'Add Client'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: editingClient ? handleEditClient : handleAddClient,\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Company Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"companyName\",\n                  value: clientForm.companyName,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Contact Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"contactName\",\n                  value: clientForm.contactName,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: clientForm.email,\n                  onChange: handleFormChange,\n                  required: true,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  name: \"website\",\n                  value: clientForm.website,\n                  onChange: handleFormChange,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  name: \"phone\",\n                  value: clientForm.phone,\n                  onChange: handleFormChange,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Industry\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"industry\",\n                  value: clientForm.industry,\n                  onChange: handleFormChange,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Product Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"productType\",\n                  value: clientForm.productType,\n                  onChange: handleFormChange,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"watches\",\n                    children: \"Watches\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"bracelets\",\n                    children: \"Bracelets\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"both\",\n                    children: \"Both\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Subscription Plan\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"subscriptionPlan\",\n                  value: clientForm.subscriptionPlan,\n                  onChange: handleFormChange,\n                  className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"basic\",\n                    children: \"Basic\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"premium\",\n                    children: \"Premium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"enterprise\",\n                    children: \"Enterprise\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), !editingClient && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"password\",\n                    value: clientForm.password,\n                    onChange: handleFormChange,\n                    required: true,\n                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: handleSuggestPassword,\n                    className: \"mt-1 px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300\",\n                    children: \"Suggest\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowModal(false),\n                  className: \"inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n                  children: editingClient ? 'Update Client' : 'Add Client'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-4 mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search clients...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full md:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedStatus,\n                onChange: e => setSelectedStatus(e.target.value),\n                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending\",\n                  children: \"Pending\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"min-w-full divide-y divide-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                className: \"bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Client\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Conversion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\",\n                    children: \"Integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                className: \"bg-white divide-y divide-gray-200\",\n                children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this) : error ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-red-600\",\n                    children: [\"Error loading clients: \", error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this) : clients.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: /*#__PURE__*/_jsxDEV(\"td\", {\n                    colSpan: \"6\",\n                    className: \"px-4 py-8 text-center text-gray-500\",\n                    children: \"No clients found\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this) : clients.map(client => {\n                  var _client$companyName, _client$analytics5, _client$analytics5$to, _client$analytics6, _client$analytics7, _client$analytics7$to, _client$analytics8, _client$analytics9, _client$analytics0, _client$analytics0$re, _client$analytics1;\n                  return /*#__PURE__*/_jsxDEV(motion.tr, {\n                    initial: {\n                      opacity: 0\n                    },\n                    animate: {\n                      opacity: 1\n                    },\n                    className: \"hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 h-10 w-10\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\",\n                            children: ((_client$companyName = client.companyName) === null || _client$companyName === void 0 ? void 0 : _client$companyName.charAt(0)) || 'C'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 636,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 635,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"ml-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: client.companyName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 641,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500\",\n                            children: client.email\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 642,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm text-gray-500 lg:hidden\",\n                            children: [((_client$analytics5 = client.analytics) === null || _client$analytics5 === void 0 ? void 0 : (_client$analytics5$to = _client$analytics5.totalSessions) === null || _client$analytics5$to === void 0 ? void 0 : _client$analytics5$to.toLocaleString()) || '0', \" try-ons \\u2022 \", ((_client$analytics6 = client.analytics) === null || _client$analytics6 === void 0 ? void 0 : _client$analytics6.conversionRate) || '0', \"% conversion\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 643,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 640,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: ((_client$analytics7 = client.analytics) === null || _client$analytics7 === void 0 ? void 0 : (_client$analytics7$to = _client$analytics7.totalSessions) === null || _client$analytics7$to === void 0 ? void 0 : _client$analytics7$to.toLocaleString()) || '0'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [((_client$analytics8 = client.analytics) === null || _client$analytics8 === void 0 ? void 0 : _client$analytics8.productCount) || '0', \" products\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 651,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: [((_client$analytics9 = client.analytics) === null || _client$analytics9 === void 0 ? void 0 : _client$analytics9.conversionRate) || '0', \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 654,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\"$\", ((_client$analytics0 = client.analytics) === null || _client$analytics0 === void 0 ? void 0 : (_client$analytics0$re = _client$analytics0.revenue) === null || _client$analytics0$re === void 0 ? void 0 : _client$analytics0$re.toLocaleString()) || '0', \" revenue\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden md:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' : client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}`,\n                          children: client.subscriptionStatus\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 659,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500\",\n                          children: formatLastActive((_client$analytics1 = client.analytics) === null || _client$analytics1 === void 0 ? void 0 : _client$analytics1.lastActive)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 666,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 658,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap hidden lg:table-cell\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' : client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`,\n                        children: client.subscriptionPlan\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-[#2D8C88] hover:text-[#2D8C88]/80 p-1\",\n                          title: \"View Details\",\n                          children: /*#__PURE__*/_jsxDEV(Eye, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 683,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 679,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-blue-600 hover:text-blue-800 p-1\",\n                          title: \"Integration Code\",\n                          children: /*#__PURE__*/_jsxDEV(Code, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 689,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 685,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-gray-600 hover:text-gray-800 p-1\",\n                          onClick: () => openEditModal(client),\n                          title: \"Edit Client\",\n                          children: /*#__PURE__*/_jsxDEV(Edit, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 696,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 691,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"text-red-600 hover:text-red-800 p-1\",\n                          onClick: () => handleDeleteClient(client._id),\n                          title: \"Delete Client\",\n                          children: /*#__PURE__*/_jsxDEV(Trash2, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 703,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 698,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 25\n                    }, this)]\n                  }, client._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n};\n_s(Clients, \"cya51ql/ObMA5d0IMTPplXvhPFQ=\");\n_c = Clients;\nexport default Clients;\nvar _c;\n$RefreshReg$(_c, \"Clients\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "motion", "Search", "Plus", "Eye", "Edit", "Trash2", "Globe", "TrendingUp", "Users", "Code", "jsxDEV", "_jsxDEV", "generatePassword", "chars", "password", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Clients", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "searchQuery", "setSearch<PERSON>uery", "selectedStatus", "setSelectedStatus", "showModal", "setShowModal", "editingClient", "setEditingClient", "clients", "setClients", "loading", "setLoading", "error", "setError", "stats", "setStats", "newClientsThisMonth", "activeRate", "tryOnsGrowth", "revenueGrowth", "clientForm", "setClientForm", "companyName", "contactName", "website", "email", "phone", "industry", "productType", "subscriptionPlan", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "fetchClients", "token", "localStorage", "getItem", "Error", "params", "URLSearchParams", "append", "response", "fetch", "process", "env", "REACT_APP_API_URL", "headers", "ok", "data", "json", "now", "Date", "firstDayOfMonth", "getFullYear", "getMonth", "filter", "client", "createdAt", "activeClients", "subscriptionStatus", "currentMonthTryOns", "reduce", "sum", "_client$analytics", "clientTryOns", "analytics", "totalSessions", "lastMonthTryOns", "_client$analytics2", "lastM<PERSON>h", "lastMonthEnd", "currentMonthRevenue", "_client$analytics3", "clientRevenue", "revenue", "lastMonthRevenue", "_client$analytics4", "err", "console", "message", "_id", "conversionRate", "productCount", "lastActive", "formatLastActive", "date", "diffInHours", "diffInDays", "diffInWeeks", "handleFormChange", "e", "name", "value", "target", "prev", "handleSuggestPassword", "resetForm", "handleAddClient", "preventDefault", "method", "body", "JSON", "stringify", "errorData", "alert", "handleEditClient", "handleDeleteClient", "clientId", "window", "confirm", "openEditModal", "openAddModal", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "delay", "c", "toFixed", "_c$analytics", "toLocaleString", "_c$analytics2", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "type", "onChange", "required", "placeholder", "colSpan", "map", "_client$companyName", "_client$analytics5", "_client$analytics5$to", "_client$analytics6", "_client$analytics7", "_client$analytics7$to", "_client$analytics8", "_client$analytics9", "_client$analytics0", "_client$analytics0$re", "_client$analytics1", "tr", "title", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/admin/Clients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport AdminSidebar from '../../components/admin/AdminSidebar';\nimport AdminNavbar from '../../components/admin/AdminNavbar';\nimport { motion } from 'framer-motion';\nimport { Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code } from 'lucide-react';\n\nfunction generatePassword() {\n  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';\n  let password = '';\n  for (let i = 0; i < 12; i++) {\n    password += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return password;\n}\n\nconst Clients = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('all');\n  const [showModal, setShowModal] = useState(false);\n  const [editingClient, setEditingClient] = useState(null);\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({\n    newClientsThisMonth: 0,\n    activeRate: 0,\n    tryOnsGrowth: 0,\n    revenueGrowth: 0\n  });\n  const [clientForm, setClientForm] = useState({\n    companyName: '',\n    contactName: '',\n    website: '',\n    email: '',\n    password: '',\n    phone: '',\n    industry: '',\n    productType: 'watches',\n    subscriptionPlan: 'basic'\n  });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Fetch clients from backend\n  useEffect(() => {\n    fetchClients();\n  }, [searchQuery, selectedStatus]);\n\n  const fetchClients = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      const params = new URLSearchParams();\n      if (searchQuery) params.append('search', searchQuery);\n      if (selectedStatus !== 'all') params.append('status', selectedStatus);\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients?${params}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch clients');\n      }\n\n      const data = await response.json();\n      setClients(data.clients || []);\n\n      // Calculate stats\n      const now = new Date();\n      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n      \n      // Calculate new clients this month\n      const newClientsThisMonth = data.clients.filter(client => \n        new Date(client.createdAt) >= firstDayOfMonth\n      ).length;\n\n      // Calculate active rate\n      const activeClients = data.clients.filter(client => \n        client.subscriptionStatus === 'active'\n      ).length;\n      const activeRate = data.clients.length > 0 ? (activeClients / data.clients.length) * 100 : 0;\n\n      // Calculate try-ons growth (comparing current month to previous month)\n      const currentMonthTryOns = data.clients.reduce((sum, client) => {\n        const clientTryOns = client.analytics?.totalSessions || 0;\n        return sum + (new Date(client.createdAt) >= firstDayOfMonth ? clientTryOns : 0);\n      }, 0);\n\n      const lastMonthTryOns = data.clients.reduce((sum, client) => {\n        const clientTryOns = client.analytics?.totalSessions || 0;\n        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);\n        return sum + (new Date(client.createdAt) >= lastMonth && new Date(client.createdAt) <= lastMonthEnd ? clientTryOns : 0);\n      }, 0);\n\n      const tryOnsGrowth = lastMonthTryOns > 0 ? ((currentMonthTryOns - lastMonthTryOns) / lastMonthTryOns) * 100 : 0;\n\n      // Calculate revenue growth\n      const currentMonthRevenue = data.clients.reduce((sum, client) => {\n        const clientRevenue = client.analytics?.revenue || 0;\n        return sum + (new Date(client.createdAt) >= firstDayOfMonth ? clientRevenue : 0);\n      }, 0);\n\n      const lastMonthRevenue = data.clients.reduce((sum, client) => {\n        const clientRevenue = client.analytics?.revenue || 0;\n        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);\n        return sum + (new Date(client.createdAt) >= lastMonth && new Date(client.createdAt) <= lastMonthEnd ? clientRevenue : 0);\n      }, 0);\n\n      const revenueGrowth = lastMonthRevenue > 0 ? ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;\n\n      setStats({\n        newClientsThisMonth,\n        activeRate,\n        tryOnsGrowth,\n        revenueGrowth\n      });\n\n    } catch (err) {\n      console.error('Error fetching clients:', err);\n      setError(err.message);\n      // Use fallback data for development\n      setClients([\n        {\n          _id: '1',\n          companyName: 'Luxury Watches Co.',\n          website: 'luxurywatches.com',\n          email: '<EMAIL>',\n          subscriptionStatus: 'active',\n          createdAt: '2024-01-15',\n          analytics: {\n            totalSessions: 3420,\n            conversionRate: '22.4',\n            revenue: 45600,\n            productCount: 24,\n            lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n          },\n          subscriptionPlan: 'premium'\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Helper function to format last active time\n  const formatLastActive = (date) => {\n    if (!date) return 'Never';\n    const now = new Date();\n    const lastActive = new Date(date);\n    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));\n\n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours} hours ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays} days ago`;\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    return `${diffInWeeks} weeks ago`;\n  };\n\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setClientForm(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSuggestPassword = () => {\n    setClientForm(prev => ({ ...prev, password: generatePassword() }));\n  };\n\n  const resetForm = () => {\n    setClientForm({\n      companyName: '',\n      contactName: '',\n      website: '',\n      email: '',\n      password: '',\n      phone: '',\n      industry: '',\n      productType: 'watches',\n      subscriptionPlan: 'basic'\n    });\n    setEditingClient(null);\n  };\n\n  const handleAddClient = async (e) => {\n    e.preventDefault();\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create client');\n      }\n\n      const data = await response.json();\n      setShowModal(false);\n      resetForm();\n      fetchClients(); // Refresh the list\n      alert('Client created successfully!');\n    } catch (err) {\n      console.error('Error creating client:', err);\n      alert(`Error: ${err.message}`);\n    }\n  };\n\n  const handleEditClient = async (e) => {\n    e.preventDefault();\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients/${editingClient._id}`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(clientForm)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to update client');\n      }\n\n      setShowModal(false);\n      resetForm();\n      fetchClients(); // Refresh the list\n      alert('Client updated successfully!');\n    } catch (err) {\n      console.error('Error updating client:', err);\n      alert(`Error: ${err.message}`);\n    }\n  };\n\n  const handleDeleteClient = async (clientId) => {\n    if (!window.confirm('Are you sure you want to delete this client?')) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/clients/${clientId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to delete client');\n      }\n\n      fetchClients(); // Refresh the list\n      alert('Client deleted successfully!');\n    } catch (err) {\n      console.error('Error deleting client:', err);\n      alert(`Error: ${err.message}`);\n    }\n  };\n\n  const openEditModal = (client) => {\n    setEditingClient(client);\n    setClientForm({\n      companyName: client.companyName || '',\n      contactName: client.contactName || '',\n      website: client.website || '',\n      email: client.email || '',\n      password: '', // Don't pre-fill password\n      phone: client.phone || '',\n      industry: client.industry || '',\n      productType: client.productType || 'watches',\n      subscriptionPlan: client.subscriptionPlan || 'basic'\n    });\n    setShowModal(true);\n  };\n\n  const openAddModal = () => {\n    resetForm();\n    setShowModal(true);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-16 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Client Management</h1>\n              <p className=\"text-gray-600\">Manage your virtual try-on clients and track their performance.</p>\n            </div>\n            <button\n              className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n              onClick={openAddModal}\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Client\n            </button>\n          </div>\n\n          {/* Stats Overview */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">+{stats.newClientsThisMonth} new</span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.1 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm font-medium text-green-600\">{stats.activeRate.toFixed(1)}%</span>\n                <span className=\"text-sm text-gray-600 ml-2\">active rate</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-white rounded-xl shadow-sm p-6\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">${loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.revenue || 0), 0).toLocaleString()}</p>\n                </div>\n                <div className=\"w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center\">\n                  <Globe className=\"h-6 w-6 text-orange-500\" />\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className={`text-sm font-medium ${stats.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                  {stats.revenueGrowth >= 0 ? '+' : ''}{stats.revenueGrowth.toFixed(1)}%\n                </span>\n                <span className=\"text-sm text-gray-600 ml-2\">this month</span>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Add Client Modal */}\n          {showModal && (\n            <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40\">\n              <div className=\"bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative\">\n                <button\n                  className=\"absolute top-3 right-3 text-gray-400 hover:text-gray-600\"\n                  onClick={() => setShowModal(false)}\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n                <h2 className=\"text-xl font-semibold mb-4\">{editingClient ? 'Edit Client' : 'Add Client'}</h2>\n                <form onSubmit={editingClient ? handleEditClient : handleAddClient} className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Company Name</label>\n                    <input\n                      type=\"text\"\n                      name=\"companyName\"\n                      value={clientForm.companyName}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Contact Name</label>\n                    <input\n                      type=\"text\"\n                      name=\"contactName\"\n                      value={clientForm.contactName}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={clientForm.email}\n                      onChange={handleFormChange}\n                      required\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Website</label>\n                    <input\n                      type=\"url\"\n                      name=\"website\"\n                      value={clientForm.website}\n                      onChange={handleFormChange}\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Phone</label>\n                    <input\n                      type=\"tel\"\n                      name=\"phone\"\n                      value={clientForm.phone}\n                      onChange={handleFormChange}\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Industry</label>\n                    <input\n                      type=\"text\"\n                      name=\"industry\"\n                      value={clientForm.industry}\n                      onChange={handleFormChange}\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Product Type</label>\n                    <select\n                      name=\"productType\"\n                      value={clientForm.productType}\n                      onChange={handleFormChange}\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    >\n                      <option value=\"watches\">Watches</option>\n                      <option value=\"bracelets\">Bracelets</option>\n                      <option value=\"both\">Both</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Subscription Plan</label>\n                    <select\n                      name=\"subscriptionPlan\"\n                      value={clientForm.subscriptionPlan}\n                      onChange={handleFormChange}\n                      className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                    >\n                      <option value=\"basic\">Basic</option>\n                      <option value=\"premium\">Premium</option>\n                      <option value=\"enterprise\">Enterprise</option>\n                    </select>\n                  </div>\n                  {!editingClient && (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n                      <div className=\"flex gap-2\">\n                        <input\n                          type=\"text\"\n                          name=\"password\"\n                          value={clientForm.password}\n                          onChange={handleFormChange}\n                          required\n                          className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={handleSuggestPassword}\n                          className=\"mt-1 px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300\"\n                        >\n                          Suggest\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                  <div className=\"flex justify-end space-x-2\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowModal(false)}\n                      className=\"inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                    >\n                      Cancel\n                    </button>\n                    <button\n                      type=\"submit\"\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\n                    >\n                      {editingClient ? 'Update Client' : 'Add Client'}\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </div>\n          )}\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-xl shadow-sm p-4 mb-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search clients...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                />\n              </div>\n              <div className=\"w-full md:w-48\">\n                <select\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Clients Table */}\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Client</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Try-Ons</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Conversion</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell\">Status</th>\n                    <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell\">Integration</th>\n                    <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {loading ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center\">\n                        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto\"></div>\n                      </td>\n                    </tr>\n                  ) : error ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-red-600\">\n                        Error loading clients: {error}\n                      </td>\n                    </tr>\n                  ) : clients.length === 0 ? (\n                    <tr>\n                      <td colSpan=\"6\" className=\"px-4 py-8 text-center text-gray-500\">\n                        No clients found\n                      </td>\n                    </tr>\n                  ) : (\n                    clients.map((client) => (\n                      <motion.tr\n                        key={client._id}\n                        initial={{ opacity: 0 }}\n                        animate={{ opacity: 1 }}\n                        className=\"hover:bg-gray-50\"\n                      >\n                        <td className=\"px-4 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"flex-shrink-0 h-10 w-10\">\n                              <div className=\"h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white\">\n                                {client.companyName?.charAt(0) || 'C'}\n                              </div>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">{client.companyName}</div>\n                              <div className=\"text-sm text-gray-500\">{client.email}</div>\n                              <div className=\"text-sm text-gray-500 lg:hidden\">\n                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons • {client.analytics?.conversionRate || '0'}% conversion\n                              </div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>\n                          <div className=\"text-sm text-gray-500\">{client.analytics?.productCount || '0'} products</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <div className=\"text-sm font-medium text-gray-900\">{client.analytics?.conversionRate || '0'}%</div>\n                          <div className=\"text-sm text-gray-500\">${client.analytics?.revenue?.toLocaleString() || '0'} revenue</div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden md:table-cell\">\n                          <div className=\"flex flex-col space-y-1\">\n                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :\n                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :\n                              'bg-yellow-100 text-yellow-800'\n                            }`}>\n                              {client.subscriptionStatus}\n                            </span>\n                            <span className=\"text-xs text-gray-500\">{formatLastActive(client.analytics?.lastActive)}</span>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap hidden lg:table-cell\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :\n                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {client.subscriptionPlan}\n                          </span>\n                        </td>\n                        <td className=\"px-4 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex justify-end space-x-2\">\n                            <button\n                              className=\"text-[#2D8C88] hover:text-[#2D8C88]/80 p-1\"\n                              title=\"View Details\"\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-blue-600 hover:text-blue-800 p-1\"\n                              title=\"Integration Code\"\n                            >\n                              <Code className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-gray-600 hover:text-gray-800 p-1\"\n                              onClick={() => openEditModal(client)}\n                              title=\"Edit Client\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              className=\"text-red-600 hover:text-red-800 p-1\"\n                              onClick={() => handleDeleteClient(client._id)}\n                              title=\"Delete Client\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </motion.tr>\n                    ))\n                  )}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default Clients; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAG,4EAA4E;EAC1F,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3BD,QAAQ,IAAID,KAAK,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EACpE;EACA,OAAON,QAAQ;AACjB;AAEA,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC;IACjC+C,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC;IAC3CqD,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTtC,QAAQ,EAAE,EAAE;IACZuC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,SAAS;IACtBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BjC,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMmC,UAAU,GAAGjC,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA5B,SAAS,CAAC,MAAM;IACd8D,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChC,WAAW,EAAEE,cAAc,CAAC,CAAC;EAEjC,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFrB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMoB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAItC,WAAW,EAAEqC,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEvC,WAAW,CAAC;MACrD,IAAIE,cAAc,KAAK,KAAK,EAAEmC,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAErC,cAAc,CAAC;MAErE,MAAMsC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,gBAAgBP,MAAM,EAAE,EAAE;QACrFQ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUZ,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACO,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM,IAAIV,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MAEA,MAAMW,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCvC,UAAU,CAACsC,IAAI,CAACvC,OAAO,IAAI,EAAE,CAAC;;MAE9B;MACA,MAAMyC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,eAAe,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,CAAC,CAAC,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEtE;MACA,MAAMrC,mBAAmB,GAAG+B,IAAI,CAACvC,OAAO,CAAC8C,MAAM,CAACC,MAAM,IACpD,IAAIL,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIL,eAChC,CAAC,CAAC1D,MAAM;;MAER;MACA,MAAMgE,aAAa,GAAGV,IAAI,CAACvC,OAAO,CAAC8C,MAAM,CAACC,MAAM,IAC9CA,MAAM,CAACG,kBAAkB,KAAK,QAChC,CAAC,CAACjE,MAAM;MACR,MAAMwB,UAAU,GAAG8B,IAAI,CAACvC,OAAO,CAACf,MAAM,GAAG,CAAC,GAAIgE,aAAa,GAAGV,IAAI,CAACvC,OAAO,CAACf,MAAM,GAAI,GAAG,GAAG,CAAC;;MAE5F;MACA,MAAMkE,kBAAkB,GAAGZ,IAAI,CAACvC,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEN,MAAM,KAAK;QAAA,IAAAO,iBAAA;QAC9D,MAAMC,YAAY,GAAG,EAAAD,iBAAA,GAAAP,MAAM,CAACS,SAAS,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,aAAa,KAAI,CAAC;QACzD,OAAOJ,GAAG,IAAI,IAAIX,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIL,eAAe,GAAGY,YAAY,GAAG,CAAC,CAAC;MACjF,CAAC,EAAE,CAAC,CAAC;MAEL,MAAMG,eAAe,GAAGnB,IAAI,CAACvC,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEN,MAAM,KAAK;QAAA,IAAAY,kBAAA;QAC3D,MAAMJ,YAAY,GAAG,EAAAI,kBAAA,GAAAZ,MAAM,CAACS,SAAS,cAAAG,kBAAA,uBAAhBA,kBAAA,CAAkBF,aAAa,KAAI,CAAC;QACzD,MAAMG,SAAS,GAAG,IAAIlB,IAAI,CAACD,GAAG,CAACG,WAAW,CAAC,CAAC,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,MAAMgB,YAAY,GAAG,IAAInB,IAAI,CAACD,GAAG,CAACG,WAAW,CAAC,CAAC,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACnE,OAAOQ,GAAG,IAAI,IAAIX,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIY,SAAS,IAAI,IAAIlB,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIa,YAAY,GAAGN,YAAY,GAAG,CAAC,CAAC;MACzH,CAAC,EAAE,CAAC,CAAC;MAEL,MAAM7C,YAAY,GAAGgD,eAAe,GAAG,CAAC,GAAI,CAACP,kBAAkB,GAAGO,eAAe,IAAIA,eAAe,GAAI,GAAG,GAAG,CAAC;;MAE/G;MACA,MAAMI,mBAAmB,GAAGvB,IAAI,CAACvC,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEN,MAAM,KAAK;QAAA,IAAAgB,kBAAA;QAC/D,MAAMC,aAAa,GAAG,EAAAD,kBAAA,GAAAhB,MAAM,CAACS,SAAS,cAAAO,kBAAA,uBAAhBA,kBAAA,CAAkBE,OAAO,KAAI,CAAC;QACpD,OAAOZ,GAAG,IAAI,IAAIX,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIL,eAAe,GAAGqB,aAAa,GAAG,CAAC,CAAC;MAClF,CAAC,EAAE,CAAC,CAAC;MAEL,MAAME,gBAAgB,GAAG3B,IAAI,CAACvC,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEN,MAAM,KAAK;QAAA,IAAAoB,kBAAA;QAC5D,MAAMH,aAAa,GAAG,EAAAG,kBAAA,GAAApB,MAAM,CAACS,SAAS,cAAAW,kBAAA,uBAAhBA,kBAAA,CAAkBF,OAAO,KAAI,CAAC;QACpD,MAAML,SAAS,GAAG,IAAIlB,IAAI,CAACD,GAAG,CAACG,WAAW,CAAC,CAAC,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,MAAMgB,YAAY,GAAG,IAAInB,IAAI,CAACD,GAAG,CAACG,WAAW,CAAC,CAAC,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACnE,OAAOQ,GAAG,IAAI,IAAIX,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIY,SAAS,IAAI,IAAIlB,IAAI,CAACK,MAAM,CAACC,SAAS,CAAC,IAAIa,YAAY,GAAGG,aAAa,GAAG,CAAC,CAAC;MAC1H,CAAC,EAAE,CAAC,CAAC;MAEL,MAAMrD,aAAa,GAAGuD,gBAAgB,GAAG,CAAC,GAAI,CAACJ,mBAAmB,GAAGI,gBAAgB,IAAIA,gBAAgB,GAAI,GAAG,GAAG,CAAC;MAEpH3D,QAAQ,CAAC;QACPC,mBAAmB;QACnBC,UAAU;QACVC,YAAY;QACZC;MACF,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOyD,GAAG,EAAE;MACZC,OAAO,CAACjE,KAAK,CAAC,yBAAyB,EAAEgE,GAAG,CAAC;MAC7C/D,QAAQ,CAAC+D,GAAG,CAACE,OAAO,CAAC;MACrB;MACArE,UAAU,CAAC,CACT;QACEsE,GAAG,EAAE,GAAG;QACRzD,WAAW,EAAE,oBAAoB;QACjCE,OAAO,EAAE,mBAAmB;QAC5BC,KAAK,EAAE,2BAA2B;QAClCiC,kBAAkB,EAAE,QAAQ;QAC5BF,SAAS,EAAE,YAAY;QACvBQ,SAAS,EAAE;UACTC,aAAa,EAAE,IAAI;UACnBe,cAAc,EAAE,MAAM;UACtBP,OAAO,EAAE,KAAK;UACdQ,YAAY,EAAE,EAAE;UAChBC,UAAU,EAAE,IAAIhC,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACtD,CAAC;QACDpB,gBAAgB,EAAE;MACpB,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwE,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IACzB,MAAMnC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMgC,UAAU,GAAG,IAAIhC,IAAI,CAACkC,IAAI,CAAC;IACjC,MAAMC,WAAW,GAAG/F,IAAI,CAACC,KAAK,CAAC,CAAC0D,GAAG,GAAGiC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErE,IAAIG,WAAW,GAAG,CAAC,EAAE,OAAO,UAAU;IACtC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,YAAY;IACvD,MAAMC,UAAU,GAAGhG,IAAI,CAACC,KAAK,CAAC8F,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAO,GAAGA,UAAU,WAAW;IACnD,MAAMC,WAAW,GAAGjG,IAAI,CAACC,KAAK,CAAC+F,UAAU,GAAG,CAAC,CAAC;IAC9C,OAAO,GAAGC,WAAW,YAAY;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvE,aAAa,CAACwE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;IAClCzE,aAAa,CAACwE,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE1G,QAAQ,EAAEF,gBAAgB,CAAC;IAAE,CAAC,CAAC,CAAC;EACpE,CAAC;EAED,MAAM8G,SAAS,GAAGA,CAAA,KAAM;IACtB1E,aAAa,CAAC;MACZC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTtC,QAAQ,EAAE,EAAE;MACZuC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,SAAS;MACtBC,gBAAgB,EAAE;IACpB,CAAC,CAAC;IACFtB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMyF,eAAe,GAAG,MAAOP,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMhE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAc,EAAE;QAC3EsD,MAAM,EAAE,MAAM;QACdrD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUZ,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDkE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjF,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACoB,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAMwD,SAAS,GAAG,MAAM9D,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIZ,KAAK,CAACkE,SAAS,CAACxB,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA,MAAM/B,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClC3C,YAAY,CAAC,KAAK,CAAC;MACnB0F,SAAS,CAAC,CAAC;MACX/D,YAAY,CAAC,CAAC,CAAC,CAAC;MAChBuE,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZC,OAAO,CAACjE,KAAK,CAAC,wBAAwB,EAAEgE,GAAG,CAAC;MAC5C2B,KAAK,CAAC,UAAU3B,GAAG,CAACE,OAAO,EAAE,CAAC;IAChC;EACF,CAAC;EAED,MAAM0B,gBAAgB,GAAG,MAAOf,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMhE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,gBAAgBtC,aAAa,CAACyE,GAAG,EAAE,EAAE;QAChGmB,MAAM,EAAE,KAAK;QACbrD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUZ,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDkE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjF,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAACoB,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAMwD,SAAS,GAAG,MAAM9D,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIZ,KAAK,CAACkE,SAAS,CAACxB,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEAzE,YAAY,CAAC,KAAK,CAAC;MACnB0F,SAAS,CAAC,CAAC;MACX/D,YAAY,CAAC,CAAC,CAAC,CAAC;MAChBuE,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZC,OAAO,CAACjE,KAAK,CAAC,wBAAwB,EAAEgE,GAAG,CAAC;MAC5C2B,KAAK,CAAC,UAAU3B,GAAG,CAACE,OAAO,EAAE,CAAC;IAChC;EACF,CAAC;EAED,MAAM2B,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MACnE;IACF;IAEA,IAAI;MACF,MAAM3E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,gBAAgB8D,QAAQ,EAAE,EAAE;QACvFR,MAAM,EAAE,QAAQ;QAChBrD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUZ,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACO,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAMwD,SAAS,GAAG,MAAM9D,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIZ,KAAK,CAACkE,SAAS,CAACxB,OAAO,IAAI,yBAAyB,CAAC;MACjE;MAEA9C,YAAY,CAAC,CAAC,CAAC,CAAC;MAChBuE,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAO3B,GAAG,EAAE;MACZC,OAAO,CAACjE,KAAK,CAAC,wBAAwB,EAAEgE,GAAG,CAAC;MAC5C2B,KAAK,CAAC,UAAU3B,GAAG,CAACE,OAAO,EAAE,CAAC;IAChC;EACF,CAAC;EAED,MAAM+B,aAAa,GAAItD,MAAM,IAAK;IAChChD,gBAAgB,CAACgD,MAAM,CAAC;IACxBlC,aAAa,CAAC;MACZC,WAAW,EAAEiC,MAAM,CAACjC,WAAW,IAAI,EAAE;MACrCC,WAAW,EAAEgC,MAAM,CAAChC,WAAW,IAAI,EAAE;MACrCC,OAAO,EAAE+B,MAAM,CAAC/B,OAAO,IAAI,EAAE;MAC7BC,KAAK,EAAE8B,MAAM,CAAC9B,KAAK,IAAI,EAAE;MACzBtC,QAAQ,EAAE,EAAE;MAAE;MACduC,KAAK,EAAE6B,MAAM,CAAC7B,KAAK,IAAI,EAAE;MACzBC,QAAQ,EAAE4B,MAAM,CAAC5B,QAAQ,IAAI,EAAE;MAC/BC,WAAW,EAAE2B,MAAM,CAAC3B,WAAW,IAAI,SAAS;MAC5CC,gBAAgB,EAAE0B,MAAM,CAAC1B,gBAAgB,IAAI;IAC/C,CAAC,CAAC;IACFxB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMyG,YAAY,GAAGA,CAAA,KAAM;IACzBf,SAAS,CAAC,CAAC;IACX1F,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,oBACErB,OAAA;IAAK+H,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtChI,OAAA,CAACb,YAAY;MAAC8I,MAAM,EAAErH,aAAc;MAACsH,OAAO,EAAEA,CAAA,KAAMrH,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAoH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjItI,OAAA,CAACZ,WAAW;MAAC0D,aAAa,EAAEA,aAAc;MAAChC,SAAS,EAAEA;IAAU;MAAAqH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnEtI,OAAA;MAAM+H,SAAS,EAAE,GAAGhF,UAAU,oCAAqC;MAAAiF,QAAA,eACjEhI,OAAA;QAAK+H,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBhI,OAAA;UAAK+H,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFhI,OAAA;YAAAgI,QAAA,gBACEhI,OAAA;cAAI+H,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvEtI,OAAA;cAAG+H,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACNtI,OAAA;YACE+H,SAAS,EAAC,6KAA6K;YACvLQ,OAAO,EAAET,YAAa;YAAAE,QAAA,gBAEtBhI,OAAA,CAACT,IAAI;cAACwI,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtI,OAAA;UAAK+H,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFhI,OAAA,CAACX,MAAM,CAACmJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BZ,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChI,OAAA;cAAK+H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAG+H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtI,OAAA;kBAAG+H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtG,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACf;gBAAM;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACNtI,OAAA;gBAAK+H,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrFhI,OAAA,CAACH,KAAK;kBAACkI,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtI,OAAA;cAAK+H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhI,OAAA;gBAAM+H,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAC,GAAC,EAAClG,KAAK,CAACE,mBAAmB,EAAC,MAAI;cAAA;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5FtI,OAAA;gBAAM+H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbtI,OAAA,CAACX,MAAM,CAACmJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChI,OAAA;cAAK+H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAG+H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnEtI,OAAA;kBAAG+H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtG,OAAO,GAAG,KAAK,GAAGF,OAAO,CAAC8C,MAAM,CAACyE,CAAC,IAAIA,CAAC,CAACrE,kBAAkB,KAAK,QAAQ,CAAC,CAACjE;gBAAM;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC,eACNtI,OAAA;gBAAK+H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFhI,OAAA,CAACJ,UAAU;kBAACmI,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtI,OAAA;cAAK+H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhI,OAAA;gBAAM+H,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAElG,KAAK,CAACG,UAAU,CAAC+G,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1FtI,OAAA;gBAAM+H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbtI,OAAA,CAACX,MAAM,CAACmJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChI,OAAA;cAAK+H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAG+H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtI,OAAA;kBAAG+H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEtG,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEkE,CAAC;oBAAA,IAAAE,YAAA;oBAAA,OAAKpE,GAAG,IAAI,EAAAoE,YAAA,GAAAF,CAAC,CAAC/D,SAAS,cAAAiE,YAAA,uBAAXA,YAAA,CAAahE,aAAa,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAACiE,cAAc,CAAC;gBAAC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrK,CAAC,eACNtI,OAAA;gBAAK+H,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFhI,OAAA,CAACR,GAAG;kBAACuI,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtI,OAAA;cAAK+H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhI,OAAA;gBAAM+H,SAAS,EAAE,uBAAuBjG,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAA8F,QAAA,GACnGlG,KAAK,CAACI,YAAY,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEJ,KAAK,CAACI,YAAY,CAAC8G,OAAO,CAAC,CAAC,CAAC,EAAC,GACrE;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPtI,OAAA;gBAAM+H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbtI,OAAA,CAACX,MAAM,CAACmJ,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7ChI,OAAA;cAAK+H,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAG+H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClEtI,OAAA;kBAAG+H,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAC,GAAC,EAACtG,OAAO,GAAG,KAAK,GAAGF,OAAO,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEkE,CAAC;oBAAA,IAAAI,aAAA;oBAAA,OAAKtE,GAAG,IAAI,EAAAsE,aAAA,GAAAJ,CAAC,CAAC/D,SAAS,cAAAmE,aAAA,uBAAXA,aAAA,CAAa1D,OAAO,KAAI,CAAC,CAAC;kBAAA,GAAE,CAAC,CAAC,CAACyD,cAAc,CAAC,CAAC;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChK,CAAC,eACNtI,OAAA;gBAAK+H,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvFhI,OAAA,CAACL,KAAK;kBAACoI,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtI,OAAA;cAAK+H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBhI,OAAA;gBAAM+H,SAAS,EAAE,uBAAuBjG,KAAK,CAACK,aAAa,IAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAA6F,QAAA,GACpGlG,KAAK,CAACK,aAAa,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEL,KAAK,CAACK,aAAa,CAAC6G,OAAO,CAAC,CAAC,CAAC,EAAC,GACvE;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPtI,OAAA;gBAAM+H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAGLlH,SAAS,iBACRpB,OAAA;UAAK+H,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzFhI,OAAA;YAAK+H,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzEhI,OAAA;cACE+H,SAAS,EAAC,0DAA0D;cACpEQ,OAAO,EAAEA,CAAA,KAAMlH,YAAY,CAAC,KAAK,CAAE;cAAA2G,QAAA,eAEnChI,OAAA;gBAAKoJ,KAAK,EAAC,4BAA4B;gBAACrB,SAAS,EAAC,SAAS;gBAACsB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAvB,QAAA,eAC/GhI,OAAA;kBAAMwJ,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTtI,OAAA;cAAI+H,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAE1G,aAAa,GAAG,aAAa,GAAG;YAAY;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9FtI,OAAA;cAAM4J,QAAQ,EAAEtI,aAAa,GAAGkG,gBAAgB,GAAGR,eAAgB;cAACe,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvFhI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EtI,OAAA;kBACE6J,IAAI,EAAC,MAAM;kBACXnD,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAEvE,UAAU,CAACE,WAAY;kBAC9BwH,QAAQ,EAAEtD,gBAAiB;kBAC3BuD,QAAQ;kBACRhC,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EtI,OAAA;kBACE6J,IAAI,EAAC,MAAM;kBACXnD,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAEvE,UAAU,CAACG,WAAY;kBAC9BuH,QAAQ,EAAEtD,gBAAiB;kBAC3BuD,QAAQ;kBACRhC,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEtI,OAAA;kBACE6J,IAAI,EAAC,OAAO;kBACZnD,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEvE,UAAU,CAACK,KAAM;kBACxBqH,QAAQ,EAAEtD,gBAAiB;kBAC3BuD,QAAQ;kBACRhC,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1EtI,OAAA;kBACE6J,IAAI,EAAC,KAAK;kBACVnD,IAAI,EAAC,SAAS;kBACdC,KAAK,EAAEvE,UAAU,CAACI,OAAQ;kBAC1BsH,QAAQ,EAAEtD,gBAAiB;kBAC3BuB,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEtI,OAAA;kBACE6J,IAAI,EAAC,KAAK;kBACVnD,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEvE,UAAU,CAACM,KAAM;kBACxBoH,QAAQ,EAAEtD,gBAAiB;kBAC3BuB,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3EtI,OAAA;kBACE6J,IAAI,EAAC,MAAM;kBACXnD,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvE,UAAU,CAACO,QAAS;kBAC3BmH,QAAQ,EAAEtD,gBAAiB;kBAC3BuB,SAAS,EAAC;gBAA+G;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/EtI,OAAA;kBACE0G,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAEvE,UAAU,CAACQ,WAAY;kBAC9BkH,QAAQ,EAAEtD,gBAAiB;kBAC3BuB,SAAS,EAAC,+GAA+G;kBAAAC,QAAA,gBAEzHhI,OAAA;oBAAQ2G,KAAK,EAAC,SAAS;oBAAAqB,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCtI,OAAA;oBAAQ2G,KAAK,EAAC,WAAW;oBAAAqB,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtI,OAAA;oBAAQ2G,KAAK,EAAC,MAAM;oBAAAqB,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNtI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpFtI,OAAA;kBACE0G,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAEvE,UAAU,CAACS,gBAAiB;kBACnCiH,QAAQ,EAAEtD,gBAAiB;kBAC3BuB,SAAS,EAAC,+GAA+G;kBAAAC,QAAA,gBAEzHhI,OAAA;oBAAQ2G,KAAK,EAAC,OAAO;oBAAAqB,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCtI,OAAA;oBAAQ2G,KAAK,EAAC,SAAS;oBAAAqB,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCtI,OAAA;oBAAQ2G,KAAK,EAAC,YAAY;oBAAAqB,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACL,CAAChH,aAAa,iBACbtB,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAO+H,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3EtI,OAAA;kBAAK+H,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBhI,OAAA;oBACE6J,IAAI,EAAC,MAAM;oBACXnD,IAAI,EAAC,UAAU;oBACfC,KAAK,EAAEvE,UAAU,CAACjC,QAAS;oBAC3B2J,QAAQ,EAAEtD,gBAAiB;oBAC3BuD,QAAQ;oBACRhC,SAAS,EAAC;kBAA+G;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC,eACFtI,OAAA;oBACE6J,IAAI,EAAC,QAAQ;oBACbtB,OAAO,EAAEzB,qBAAsB;oBAC/BiB,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,EACzE;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eACDtI,OAAA;gBAAK+H,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzChI,OAAA;kBACE6J,IAAI,EAAC,QAAQ;kBACbtB,OAAO,EAAEA,CAAA,KAAMlH,YAAY,CAAC,KAAK,CAAE;kBACnC0G,SAAS,EAAC,uNAAuN;kBAAAC,QAAA,EAClO;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtI,OAAA;kBACE6J,IAAI,EAAC,QAAQ;kBACb9B,SAAS,EAAC,6NAA6N;kBAAAC,QAAA,EAEtO1G,aAAa,GAAG,eAAe,GAAG;gBAAY;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDtI,OAAA;UAAK+H,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDhI,OAAA;YAAK+H,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9ChI,OAAA;cAAK+H,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBhI,OAAA;gBACE6J,IAAI,EAAC,MAAM;gBACXG,WAAW,EAAC,mBAAmB;gBAC/BrD,KAAK,EAAE3F,WAAY;gBACnB8I,QAAQ,EAAGrD,CAAC,IAAKxF,cAAc,CAACwF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAChDoB,SAAS,EAAC;cAAkI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtI,OAAA;cAAK+H,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BhI,OAAA;gBACE2G,KAAK,EAAEzF,cAAe;gBACtB4I,QAAQ,EAAGrD,CAAC,IAAKtF,iBAAiB,CAACsF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBACnDoB,SAAS,EAAC,kIAAkI;gBAAAC,QAAA,gBAE5IhI,OAAA;kBAAQ2G,KAAK,EAAC,KAAK;kBAAAqB,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvCtI,OAAA;kBAAQ2G,KAAK,EAAC,QAAQ;kBAAAqB,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtI,OAAA;kBAAQ2G,KAAK,EAAC,SAAS;kBAAAqB,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtI,OAAA;UAAK+H,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DhI,OAAA;YAAK+H,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhI,OAAA;cAAO+H,SAAS,EAAC,qCAAqC;cAAAC,QAAA,gBACpDhI,OAAA;gBAAO+H,SAAS,EAAC,YAAY;gBAAAC,QAAA,eAC3BhI,OAAA;kBAAAgI,QAAA,gBACEhI,OAAA;oBAAI+H,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1GtI,OAAA;oBAAI+H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChItI,OAAA;oBAAI+H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnItI,OAAA;oBAAI+H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/HtI,OAAA;oBAAI+H,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpItI,OAAA;oBAAI+H,SAAS,EAAC,iFAAiF;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRtI,OAAA;gBAAO+H,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EACjDtG,OAAO,gBACN1B,OAAA;kBAAAgI,QAAA,eACEhI,OAAA;oBAAIiK,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,eAC/ChI,OAAA;sBAAK+H,SAAS,EAAC;oBAAuE;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH1G,KAAK,gBACP5B,OAAA;kBAAAgI,QAAA,eACEhI,OAAA;oBAAIiK,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,yBACtC,EAACpG,KAAK;kBAAA;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACH9G,OAAO,CAACf,MAAM,KAAK,CAAC,gBACtBT,OAAA;kBAAAgI,QAAA,eACEhI,OAAA;oBAAIiK,OAAO,EAAC,GAAG;oBAAClC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GAEL9G,OAAO,CAAC0I,GAAG,CAAE3F,MAAM;kBAAA,IAAA4F,mBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA;kBAAA,oBACjB7K,OAAA,CAACX,MAAM,CAACyL,EAAE;oBAERrC,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAE;oBACxBE,OAAO,EAAE;sBAAEF,OAAO,EAAE;oBAAE,CAAE;oBACxBX,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5BhI,OAAA;sBAAI+H,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,eACzChI,OAAA;wBAAK+H,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,gBAChChI,OAAA;0BAAK+H,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,eACtChI,OAAA;4BAAK+H,SAAS,EAAC,iFAAiF;4BAAAC,QAAA,EAC7F,EAAAmC,mBAAA,GAAA5F,MAAM,CAACjC,WAAW,cAAA6H,mBAAA,uBAAlBA,mBAAA,CAAoB9J,MAAM,CAAC,CAAC,CAAC,KAAI;0BAAG;4BAAA8H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNtI,OAAA;0BAAK+H,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnBhI,OAAA;4BAAK+H,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAEzD,MAAM,CAACjC;0BAAW;4BAAA6F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC7EtI,OAAA;4BAAK+H,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAEzD,MAAM,CAAC9B;0BAAK;4BAAA0F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eAC3DtI,OAAA;4BAAK+H,SAAS,EAAC,iCAAiC;4BAAAC,QAAA,GAC7C,EAAAoC,kBAAA,GAAA7F,MAAM,CAACS,SAAS,cAAAoF,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBnF,aAAa,cAAAoF,qBAAA,uBAA/BA,qBAAA,CAAiCnB,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,kBAAW,EAAC,EAAAoB,kBAAA,GAAA/F,MAAM,CAACS,SAAS,cAAAsF,kBAAA,uBAAhBA,kBAAA,CAAkBtE,cAAc,KAAI,GAAG,EAAC,cAChH;0BAAA;4BAAAmC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtI,OAAA;sBAAI+H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9DhI,OAAA;wBAAK+H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAE,EAAAuC,kBAAA,GAAAhG,MAAM,CAACS,SAAS,cAAAuF,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBtF,aAAa,cAAAuF,qBAAA,uBAA/BA,qBAAA,CAAiCtB,cAAc,CAAC,CAAC,KAAI;sBAAG;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnHtI,OAAA;wBAAK+H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAE,EAAAyC,kBAAA,GAAAlG,MAAM,CAACS,SAAS,cAAAyF,kBAAA,uBAAhBA,kBAAA,CAAkBxE,YAAY,KAAI,GAAG,EAAC,WAAS;sBAAA;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACLtI,OAAA;sBAAI+H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,gBAC9DhI,OAAA;wBAAK+H,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,GAAE,EAAA0C,kBAAA,GAAAnG,MAAM,CAACS,SAAS,cAAA0F,kBAAA,uBAAhBA,kBAAA,CAAkB1E,cAAc,KAAI,GAAG,EAAC,GAAC;sBAAA;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnGtI,OAAA;wBAAK+H,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAAC,EAAC,EAAA2C,kBAAA,GAAApG,MAAM,CAACS,SAAS,cAAA2F,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBlF,OAAO,cAAAmF,qBAAA,uBAAzBA,qBAAA,CAA2B1B,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,UAAQ;sBAAA;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC,eACLtI,OAAA;sBAAI+H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DhI,OAAA;wBAAK+H,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtChI,OAAA;0BAAM+H,SAAS,EAAE,2EACfxD,MAAM,CAACG,kBAAkB,KAAK,QAAQ,GAAG,6BAA6B,GACtEH,MAAM,CAACG,kBAAkB,KAAK,OAAO,GAAG,2BAA2B,GACnE,+BAA+B,EAC9B;0BAAAsD,QAAA,EACAzD,MAAM,CAACG;wBAAkB;0BAAAyD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACPtI,OAAA;0BAAM+H,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,EAAE7B,gBAAgB,EAAA0E,kBAAA,GAACtG,MAAM,CAACS,SAAS,cAAA6F,kBAAA,uBAAhBA,kBAAA,CAAkB3E,UAAU;wBAAC;0BAAAiC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLtI,OAAA;sBAAI+H,SAAS,EAAC,kDAAkD;sBAAAC,QAAA,eAC9DhI,OAAA;wBAAM+H,SAAS,EAAE,2EACfxD,MAAM,CAAC1B,gBAAgB,KAAK,YAAY,GAAG,+BAA+B,GAC1E0B,MAAM,CAAC1B,gBAAgB,KAAK,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,EAChG;wBAAAmF,QAAA,EACAzD,MAAM,CAAC1B;sBAAgB;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACLtI,OAAA;sBAAI+H,SAAS,EAAC,4DAA4D;sBAAAC,QAAA,eACxEhI,OAAA;wBAAK+H,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzChI,OAAA;0BACE+H,SAAS,EAAC,4CAA4C;0BACtDgD,KAAK,EAAC,cAAc;0BAAA/C,QAAA,eAEpBhI,OAAA,CAACR,GAAG;4BAACuI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrB,CAAC,eACTtI,OAAA;0BACE+H,SAAS,EAAC,uCAAuC;0BACjDgD,KAAK,EAAC,kBAAkB;0BAAA/C,QAAA,eAExBhI,OAAA,CAACF,IAAI;4BAACiI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTtI,OAAA;0BACE+H,SAAS,EAAC,uCAAuC;0BACjDQ,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAACtD,MAAM,CAAE;0BACrCwG,KAAK,EAAC,aAAa;0BAAA/C,QAAA,eAEnBhI,OAAA,CAACP,IAAI;4BAACsI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC,eACTtI,OAAA;0BACE+H,SAAS,EAAC,qCAAqC;0BAC/CQ,OAAO,EAAEA,CAAA,KAAMd,kBAAkB,CAAClD,MAAM,CAACwB,GAAG,CAAE;0BAC9CgF,KAAK,EAAC,eAAe;0BAAA/C,QAAA,eAErBhI,OAAA,CAACN,MAAM;4BAACqI,SAAS,EAAC;0BAAS;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GA9EA/D,MAAM,CAACwB,GAAG;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA+EN,CAAC;gBAAA,CACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3H,EAAA,CA9rBID,OAAO;AAAAsK,EAAA,GAAPtK,OAAO;AAgsBb,eAAeA,OAAO;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}