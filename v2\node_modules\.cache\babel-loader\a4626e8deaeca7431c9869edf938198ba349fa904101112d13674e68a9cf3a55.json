{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\ClientSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Loader, AlertCircle, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientSettings = () => {\n  _s();\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState({\n    type: '',\n    text: ''\n  });\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n  const [profile, setProfile] = useState({\n    companyName: '',\n    contactName: '',\n    email: '',\n    phone: '',\n    website: '',\n    industry: '',\n    productType: 'watches'\n  });\n\n  // Load user profile data\n  useEffect(() => {\n    const loadProfile = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          setMessage({\n            type: 'error',\n            text: 'No authentication token found'\n          });\n          return;\n        }\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (response.ok) {\n          const userData = await response.json();\n          setProfile({\n            companyName: userData.companyName || '',\n            contactName: userData.contactName || '',\n            email: userData.email || '',\n            phone: userData.phone || '',\n            website: userData.website || '',\n            industry: userData.industry || '',\n            productType: userData.productType || 'watches'\n          });\n        } else {\n          setMessage({\n            type: 'error',\n            text: 'Failed to load profile data'\n          });\n        }\n      } catch (error) {\n        console.error('Error loading profile:', error);\n        setMessage({\n          type: 'error',\n          text: 'Error loading profile data'\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadProfile();\n  }, []);\n  const handleInputChange = (field, value) => {\n    setProfile(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear any existing messages when user starts typing\n    if (message.text) {\n      setMessage({\n        type: '',\n        text: ''\n      });\n    }\n  };\n  const handleSave = async () => {\n    try {\n      setSaving(true);\n      setMessage({\n        type: '',\n        text: ''\n      });\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setMessage({\n          type: 'error',\n          text: 'No authentication token found'\n        });\n        return;\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(profile)\n      });\n      if (response.ok) {\n        const result = await response.json();\n        setMessage({\n          type: 'success',\n          text: 'Profile updated successfully!'\n        });\n\n        // Update localStorage with new user data\n        localStorage.setItem('user', JSON.stringify(result.user));\n      } else {\n        const errorData = await response.json();\n        setMessage({\n          type: 'error',\n          text: errorData.message || 'Failed to update profile'\n        });\n      }\n    } catch (error) {\n      console.error('Error saving profile:', error);\n      setMessage({\n        type: 'error',\n        text: 'Error saving profile data'\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n  const renderProfileContent = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Loader, {\n          className: \"h-8 w-8 animate-spin text-[#2D8C88]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-2 text-gray-600\",\n          children: \"Loading profile...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [message.text && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 rounded-lg flex items-center ${message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'}`,\n        children: [message.type === 'success' ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 15\n        }, this), message.text]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-4\",\n          children: \"Profile Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Company Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: profile.companyName,\n              onChange: e => handleInputChange('companyName', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Contact Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: profile.contactName,\n              onChange: e => handleInputChange('contactName', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Email Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              value: profile.email,\n              onChange: e => handleInputChange('email', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              value: profile.phone,\n              onChange: e => handleInputChange('phone', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Website URL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              value: profile.website,\n              onChange: e => handleInputChange('website', e.target.value),\n              placeholder: \"https://yourcompany.com\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Industry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: profile.industry,\n              onChange: e => handleInputChange('industry', e.target.value),\n              placeholder: \"e.g., Fashion, Jewelry, Watches\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Primary Product Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: profile.productType,\n              onChange: e => handleInputChange('productType', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"watches\",\n                children: \"Watches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"bracelets\",\n                children: \"Bracelets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"both\",\n                children: \"Both Watches & Bracelets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: \"This will be the default product type for your embed codes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-20 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Profile Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your company profile and contact information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b border-gray-200 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"h-5 w-5 text-[#2D8C88] mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: \"Profile Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: renderProfileContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              disabled: saving,\n              className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: [saving ? /*#__PURE__*/_jsxDEV(Loader, {\n                className: \"h-4 w-4 mr-2 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Save, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), saving ? 'Saving...' : 'Save Changes']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientSettings, \"2QVlZgPaZI4VayRVQzsAovsJs+I=\");\n_c = ClientSettings;\nexport default ClientSettings;\nvar _c;\n$RefreshReg$(_c, \"ClientSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ClientSidebar", "ClientNavbar", "motion", "Save", "User", "Loader", "AlertCircle", "CheckCircle", "jsxDEV", "_jsxDEV", "ClientSettings", "_s", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "loading", "setLoading", "saving", "setSaving", "message", "setMessage", "type", "text", "toggleSidebar", "<PERSON><PERSON><PERSON><PERSON>", "profile", "setProfile", "companyName", "contactName", "email", "phone", "website", "industry", "productType", "loadProfile", "token", "localStorage", "getItem", "response", "fetch", "process", "env", "REACT_APP_API_URL", "headers", "ok", "userData", "json", "error", "console", "handleInputChange", "field", "value", "prev", "handleSave", "method", "body", "JSON", "stringify", "result", "setItem", "user", "errorData", "renderProfileContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "required", "placeholder", "isOpen", "onClose", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/ClientSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport { motion } from 'framer-motion';\nimport { Save, User, Loader, AlertCircle, CheckCircle } from 'lucide-react';\n\nconst ClientSettings = () => {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [message, setMessage] = useState({ type: '', text: '' });\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  const [profile, setProfile] = useState({\n    companyName: '',\n    contactName: '',\n    email: '',\n    phone: '',\n    website: '',\n    industry: '',\n    productType: 'watches'\n  });\n\n  // Load user profile data\n  useEffect(() => {\n    const loadProfile = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n\n        if (!token) {\n          setMessage({ type: 'error', text: 'No authentication token found' });\n          return;\n        }\n\n        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const userData = await response.json();\n          setProfile({\n            companyName: userData.companyName || '',\n            contactName: userData.contactName || '',\n            email: userData.email || '',\n            phone: userData.phone || '',\n            website: userData.website || '',\n            industry: userData.industry || '',\n            productType: userData.productType || 'watches'\n          });\n        } else {\n          setMessage({ type: 'error', text: 'Failed to load profile data' });\n        }\n      } catch (error) {\n        console.error('Error loading profile:', error);\n        setMessage({ type: 'error', text: 'Error loading profile data' });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadProfile();\n  }, []);\n\n  const handleInputChange = (field, value) => {\n    setProfile(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear any existing messages when user starts typing\n    if (message.text) {\n      setMessage({ type: '', text: '' });\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      setSaving(true);\n      setMessage({ type: '', text: '' });\n\n      const token = localStorage.getItem('token');\n\n      if (!token) {\n        setMessage({ type: 'error', text: 'No authentication token found' });\n        return;\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(profile)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setMessage({ type: 'success', text: 'Profile updated successfully!' });\n\n        // Update localStorage with new user data\n        localStorage.setItem('user', JSON.stringify(result.user));\n      } else {\n        const errorData = await response.json();\n        setMessage({\n          type: 'error',\n          text: errorData.message || 'Failed to update profile'\n        });\n      }\n    } catch (error) {\n      console.error('Error saving profile:', error);\n      setMessage({ type: 'error', text: 'Error saving profile data' });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const renderProfileContent = () => {\n    if (loading) {\n      return (\n        <div className=\"flex items-center justify-center py-12\">\n          <Loader className=\"h-8 w-8 animate-spin text-[#2D8C88]\" />\n          <span className=\"ml-2 text-gray-600\">Loading profile...</span>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-6\">\n        {/* Message Display */}\n        {message.text && (\n          <div className={`p-4 rounded-lg flex items-center ${\n            message.type === 'success'\n              ? 'bg-green-50 text-green-800 border border-green-200'\n              : 'bg-red-50 text-red-800 border border-red-200'\n          }`}>\n            {message.type === 'success' ? (\n              <CheckCircle className=\"h-5 w-5 mr-2\" />\n            ) : (\n              <AlertCircle className=\"h-5 w-5 mr-2\" />\n            )}\n            {message.text}\n          </div>\n        )}\n\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company Name *\n              </label>\n              <input\n                type=\"text\"\n                value={profile.companyName}\n                onChange={(e) => handleInputChange('companyName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Contact Name *\n              </label>\n              <input\n                type=\"text\"\n                value={profile.contactName}\n                onChange={(e) => handleInputChange('contactName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Email Address *\n              </label>\n              <input\n                type=\"email\"\n                value={profile.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                value={profile.phone}\n                onChange={(e) => handleInputChange('phone', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Website URL\n              </label>\n              <input\n                type=\"url\"\n                value={profile.website}\n                onChange={(e) => handleInputChange('website', e.target.value)}\n                placeholder=\"https://yourcompany.com\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Industry\n              </label>\n              <input\n                type=\"text\"\n                value={profile.industry}\n                onChange={(e) => handleInputChange('industry', e.target.value)}\n                placeholder=\"e.g., Fashion, Jewelry, Watches\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Primary Product Type\n              </label>\n              <select\n                value={profile.productType}\n                onChange={(e) => handleInputChange('productType', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent\"\n              >\n                <option value=\"watches\">Watches</option>\n                <option value=\"bracelets\">Bracelets</option>\n                <option value=\"both\">Both Watches & Bracelets</option>\n              </select>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                This will be the default product type for your embed codes\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\n      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\n\n      {/* Main Content */}\n      <main className={`${mainMargin} pt-20 transition-all duration-300`}>\n        <div className=\"p-4 md:p-6\">\n          {/* Page Header */}\n          <div className=\"mb-6\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Profile Settings</h1>\n            <p className=\"text-gray-600\">Manage your company profile and contact information</p>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n            {/* Header */}\n            <div className=\"border-b border-gray-200 px-6 py-4\">\n              <div className=\"flex items-center\">\n                <User className=\"h-5 w-5 text-[#2D8C88] mr-2\" />\n                <h2 className=\"text-lg font-medium text-gray-900\">Profile Information</h2>\n              </div>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-6\">\n              {renderProfileContent()}\n            </div>\n\n            {/* Save Button */}\n            <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end\">\n              <button\n                onClick={handleSave}\n                disabled={saving}\n                className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {saving ? (\n                  <Loader className=\"h-4 w-4 mr-2 animate-spin\" />\n                ) : (\n                  <Save className=\"h-4 w-4 mr-2\" />\n                )}\n                {saving ? 'Saving...' : 'Save Changes'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default ClientSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC;IAAEwB,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC,CAAC;EAE9D,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BX,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMa,UAAU,GAAGX,SAAS,GAAG,cAAc,GAAG,eAAe;EAE/D,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC;IACrC8B,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMoC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFlB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMmB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAI,CAACF,KAAK,EAAE;UACVf,UAAU,CAAC;YAAEC,IAAI,EAAE,OAAO;YAAEC,IAAI,EAAE;UAAgC,CAAC,CAAC;UACpE;QACF;QAEA,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAc,EAAE;UAC3EC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUR,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIG,QAAQ,CAACM,EAAE,EAAE;UACf,MAAMC,QAAQ,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UACtCpB,UAAU,CAAC;YACTC,WAAW,EAAEkB,QAAQ,CAAClB,WAAW,IAAI,EAAE;YACvCC,WAAW,EAAEiB,QAAQ,CAACjB,WAAW,IAAI,EAAE;YACvCC,KAAK,EAAEgB,QAAQ,CAAChB,KAAK,IAAI,EAAE;YAC3BC,KAAK,EAAEe,QAAQ,CAACf,KAAK,IAAI,EAAE;YAC3BC,OAAO,EAAEc,QAAQ,CAACd,OAAO,IAAI,EAAE;YAC/BC,QAAQ,EAAEa,QAAQ,CAACb,QAAQ,IAAI,EAAE;YACjCC,WAAW,EAAEY,QAAQ,CAACZ,WAAW,IAAI;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLb,UAAU,CAAC;YAAEC,IAAI,EAAE,OAAO;YAAEC,IAAI,EAAE;UAA8B,CAAC,CAAC;QACpE;MACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C3B,UAAU,CAAC;UAAEC,IAAI,EAAE,OAAO;UAAEC,IAAI,EAAE;QAA6B,CAAC,CAAC;MACnE,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CzB,UAAU,CAAC0B,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;IACH;IACA,IAAIhC,OAAO,CAACG,IAAI,EAAE;MAChBF,UAAU,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;IACpC;EACF,CAAC;EAED,MAAM+B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFnC,SAAS,CAAC,IAAI,CAAC;MACfE,UAAU,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;MAElC,MAAMa,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,IAAI,CAACF,KAAK,EAAE;QACVf,UAAU,CAAC;UAAEC,IAAI,EAAE,OAAO;UAAEC,IAAI,EAAE;QAAgC,CAAC,CAAC;QACpE;MACF;MAEA,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,mBAAmB,EAAE;QAChFY,MAAM,EAAE,KAAK;QACbX,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB,CAAC;QACDoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAChC,OAAO;MAC9B,CAAC,CAAC;MAEF,IAAIa,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMc,MAAM,GAAG,MAAMpB,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACpC1B,UAAU,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAgC,CAAC,CAAC;;QAEtE;QACAc,YAAY,CAACuB,OAAO,CAAC,MAAM,EAAEH,IAAI,CAACC,SAAS,CAACC,MAAM,CAACE,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM;QACL,MAAMC,SAAS,GAAG,MAAMvB,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvC1B,UAAU,CAAC;UACTC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAEuC,SAAS,CAAC1C,OAAO,IAAI;QAC7B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C3B,UAAU,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE;MAA4B,CAAC,CAAC;IAClE,CAAC,SAAS;MACRJ,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM4C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI/C,OAAO,EAAE;MACX,oBACEP,OAAA;QAAKuD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxD,OAAA,CAACJ,MAAM;UAAC2D,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1D5D,OAAA;UAAMuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAEV;IAEA,oBACE5D,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,GAEvB7C,OAAO,CAACG,IAAI,iBACXd,OAAA;QAAKuD,SAAS,EAAE,oCACd5C,OAAO,CAACE,IAAI,KAAK,SAAS,GACtB,oDAAoD,GACpD,8CAA8C,EACjD;QAAA2C,QAAA,GACA7C,OAAO,CAACE,IAAI,KAAK,SAAS,gBACzBb,OAAA,CAACF,WAAW;UAACyD,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAExC5D,OAAA,CAACH,WAAW;UAAC0D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACxC,EACAjD,OAAO,CAACG,IAAI;MAAA;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAED5D,OAAA;QAAAwD,QAAA,gBACExD,OAAA;UAAIuD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/E5D,OAAA;UAAKuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,MAAM;cACX8B,KAAK,EAAE1B,OAAO,CAACE,WAAY;cAC3B0C,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,aAAa,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAClEY,SAAS,EAAC,kIAAkI;cAC5IS,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,MAAM;cACX8B,KAAK,EAAE1B,OAAO,CAACG,WAAY;cAC3ByC,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,aAAa,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAClEY,SAAS,EAAC,kIAAkI;cAC5IS,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,OAAO;cACZ8B,KAAK,EAAE1B,OAAO,CAACI,KAAM;cACrBwC,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,OAAO,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAC5DY,SAAS,EAAC,kIAAkI;cAC5IS,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,KAAK;cACV8B,KAAK,EAAE1B,OAAO,CAACK,KAAM;cACrBuC,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,OAAO,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAC5DY,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,KAAK;cACV8B,KAAK,EAAE1B,OAAO,CAACM,OAAQ;cACvBsC,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,SAAS,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAC9DsB,WAAW,EAAC,yBAAyB;cACrCV,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,MAAM;cACX8B,KAAK,EAAE1B,OAAO,CAACO,QAAS;cACxBqC,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,UAAU,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAC/DsB,WAAW,EAAC,iCAAiC;cAC7CV,SAAS,EAAC;YAAkI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE2C,KAAK,EAAE1B,OAAO,CAACQ,WAAY;cAC3BoC,QAAQ,EAAGC,CAAC,IAAKrB,iBAAiB,CAAC,aAAa,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cAClEY,SAAS,EAAC,kIAAkI;cAAAC,QAAA,gBAE5IxD,OAAA;gBAAQ2C,KAAK,EAAC,SAAS;gBAAAa,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC5D,OAAA;gBAAQ2C,KAAK,EAAC,WAAW;gBAAAa,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C5D,OAAA;gBAAQ2C,KAAK,EAAC,MAAM;gBAAAa,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACT5D,OAAA;cAAGuD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAID,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCxD,OAAA,CAACT,aAAa;MAAC2E,MAAM,EAAE/D,aAAc;MAACgE,OAAO,EAAEA,CAAA,KAAM/D,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClI5D,OAAA,CAACR,YAAY;MAACuB,aAAa,EAAEA,aAAc;MAACV,SAAS,EAAEA;IAAU;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpE5D,OAAA;MAAMuD,SAAS,EAAE,GAAGvC,UAAU,oCAAqC;MAAAwC,QAAA,eACjExD,OAAA;QAAKuD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAEzBxD,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAIuD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE5D,OAAA;YAAGuD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAEN5D,OAAA;UAAKuD,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAE5DxD,OAAA;YAAKuD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjDxD,OAAA;cAAKuD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxD,OAAA,CAACL,IAAI;gBAAC4D,SAAS,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChD5D,OAAA;gBAAIuD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5D,OAAA;YAAKuD,SAAS,EAAC,KAAK;YAAAC,QAAA,EACjBF,oBAAoB,CAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAGN5D,OAAA;YAAKuD,SAAS,EAAC,gEAAgE;YAAAC,QAAA,eAC7ExD,OAAA;cACEoE,OAAO,EAAEvB,UAAW;cACpBwB,QAAQ,EAAE5D,MAAO;cACjB8C,SAAS,EAAC,6NAA6N;cAAAC,QAAA,GAEtO/C,MAAM,gBACLT,OAAA,CAACJ,MAAM;gBAAC2D,SAAS,EAAC;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEhD5D,OAAA,CAACN,IAAI;gBAAC6D,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACjC,EACAnD,MAAM,GAAG,WAAW,GAAG,cAAc;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAxSID,cAAc;AAAAqE,EAAA,GAAdrE,cAAc;AA0SpB,eAAeA,cAAc;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}