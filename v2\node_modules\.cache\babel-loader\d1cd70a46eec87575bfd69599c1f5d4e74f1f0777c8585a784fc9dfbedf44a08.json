{"ast": null, "code": "var _jsxFileName = \"D:\\\\Via\\\\New folder\\\\v2\\\\src\\\\pages\\\\client\\\\ClientDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport ClientSidebar from '../../components/client/ClientSidebar';\nimport ClientNavbar from '../../components/client/ClientNavbar';\nimport EmbedCodeGenerator from '../../components/EmbedCodeGenerator';\nimport { motion } from 'framer-motion';\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';\nimport { Eye, TrendingUp, Users, ShoppingCart, Clock, Code, Globe, Smartphone } from 'lucide-react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientDashboard = () => {\n  _s();\n  var _analyticsData$trends, _analyticsData$produc, _analyticsData$device, _analyticsData$overvi, _analyticsData$overvi2, _analyticsData$overvi3, _analyticsData$overvi4, _analyticsData$overvi5, _analyticsData$overvi6, _analyticsData$overvi7, _analyticsData$overvi8, _analyticsData$overvi9, _analyticsData$overvi0, _analyticsData$overvi1, _analyticsData$overvi10, _analyticsData$overvi11, _analyticsData$overvi12, _analyticsData$overvi13, _analyticsData$recent;\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [collapsed, setCollapsed] = useState(false);\n  const [timeRange, setTimeRange] = useState('7d');\n  const [showEmbedModal, setShowEmbedModal] = useState(false);\n  const [clientData, setClientData] = useState({\n    id: '',\n    totalTryOns: 0,\n    conversionRate: 0,\n    avgDuration: 0,\n    uniqueUsers: 0,\n    revenue: 0\n  });\n  const [user, setUser] = useState(null);\n  const [analyticsData, setAnalyticsData] = useState({\n    trends: [],\n    products: [],\n    devices: [],\n    overview: null,\n    recentActivity: []\n  });\n  const [loading, setLoading] = useState(true);\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Load client data from localStorage and API\n  useEffect(() => {\n    const loadClientData = async () => {\n      try {\n        setLoading(true);\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('No authentication token found');\n        }\n\n        // Fetch client profile\n        const profileResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/auth/me`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        if (profileResponse.data) {\n          setClientData(prev => ({\n            ...prev,\n            companyName: profileResponse.data.companyName || '',\n            email: profileResponse.data.email || ''\n          }));\n        }\n\n        // Fetch analytics data\n        const [analyticsResponse, recentActivityResponse] = await Promise.all([axios.get(`${process.env.REACT_APP_API_URL}/api/analytics/client/overview`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          params: {\n            timeRange\n          }\n        }), axios.get(`${process.env.REACT_APP_API_URL}/api/analytics/client/recent-activity`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          params: {\n            timeRange\n          }\n        })]);\n        if (analyticsResponse.data) {\n          setAnalyticsData(analyticsResponse.data);\n        }\n        if (recentActivityResponse.data) {\n          setAnalyticsData(prev => ({\n            ...prev,\n            recentActivity: recentActivityResponse.data\n          }));\n        }\n      } catch (error) {\n        console.error('Error loading client data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadClientData();\n  }, [timeRange]);\n\n  // Calculate margin for main content\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\n\n  // Format data for charts\n  const tryOnTrends = ((_analyticsData$trends = analyticsData.trends) === null || _analyticsData$trends === void 0 ? void 0 : _analyticsData$trends.map(trend => ({\n    date: trend._id,\n    tryOns: trend.sessions,\n    conversions: trend.conversions\n  }))) || [];\n  const productPerformance = ((_analyticsData$produc = analyticsData.products) === null || _analyticsData$produc === void 0 ? void 0 : _analyticsData$produc.slice(0, 5).map(product => ({\n    name: product.productName || 'Unknown Product',\n    tryOns: product.sessions,\n    conversions: product.conversions\n  }))) || [];\n  const deviceStats = ((_analyticsData$device = analyticsData.devices) === null || _analyticsData$device === void 0 ? void 0 : _analyticsData$device.map((device, index) => ({\n    name: device._id,\n    value: device.sessions,\n    color: ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 5]\n  }))) || [];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n        isOpen: isSidebarOpen,\n        onClose: () => setIsSidebarOpen(false),\n        collapsed: collapsed,\n        setCollapsed: setCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n        toggleSidebar: toggleSidebar,\n        collapsed: collapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `${mainMargin} pt-20 transition-all duration-300`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 md:p-6 space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-pulse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 bg-gray-200 rounded w-1/3 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-1/2 mb-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-4 md:gap-6 mb-6\",\n              children: [1, 2, 3, 4, 5].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-8 bg-gray-200 rounded w-1/2 mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-1/4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n              children: [1, 2].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-80 bg-gray-200 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(ClientSidebar, {\n      isOpen: isSidebarOpen,\n      onClose: () => setIsSidebarOpen(false),\n      collapsed: collapsed,\n      setCollapsed: setCollapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientNavbar, {\n      toggleSidebar: toggleSidebar,\n      collapsed: collapsed\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `${mainMargin} pt-20 transition-all duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Virtual Try-On Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Monitor your product performance and customer engagement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 md:mt-0 flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex rounded-lg border border-gray-200 p-1\",\n              children: ['7d', '30d', '90d', '1y'].map(range => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setTimeRange(range),\n                className: `px-3 py-1 text-sm font-medium rounded-md ${timeRange === range ? 'bg-[#2D8C88] text-white' : 'text-gray-600 hover:text-gray-900'}`,\n                children: range\n              }, range, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowEmbedModal(true),\n              className: \"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\",\n              children: [/*#__PURE__*/_jsxDEV(Code, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), \"Get Embed Code\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-4 md:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Total Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clientData.totalTryOns.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-6 w-6 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${((_analyticsData$overvi = analyticsData.overview) === null || _analyticsData$overvi === void 0 ? void 0 : _analyticsData$overvi.tryOnsChange) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [((_analyticsData$overvi2 = analyticsData.overview) === null || _analyticsData$overvi2 === void 0 ? void 0 : _analyticsData$overvi2.tryOnsChange) >= 0 ? '+' : '', (_analyticsData$overvi3 = analyticsData.overview) === null || _analyticsData$overvi3 === void 0 ? void 0 : _analyticsData$overvi3.tryOnsChange, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.1\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Conversion Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [clientData.conversionRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${((_analyticsData$overvi4 = analyticsData.overview) === null || _analyticsData$overvi4 === void 0 ? void 0 : _analyticsData$overvi4.conversionChange) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [((_analyticsData$overvi5 = analyticsData.overview) === null || _analyticsData$overvi5 === void 0 ? void 0 : _analyticsData$overvi5.conversionChange) >= 0 ? '+' : '', (_analyticsData$overvi6 = analyticsData.overview) === null || _analyticsData$overvi6 === void 0 ? void 0 : _analyticsData$overvi6.conversionChange, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Avg Duration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [Math.floor(clientData.avgDuration / 60), \"m \", clientData.avgDuration % 60, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${((_analyticsData$overvi7 = analyticsData.overview) === null || _analyticsData$overvi7 === void 0 ? void 0 : _analyticsData$overvi7.durationChange) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [((_analyticsData$overvi8 = analyticsData.overview) === null || _analyticsData$overvi8 === void 0 ? void 0 : _analyticsData$overvi8.durationChange) >= 0 ? '+' : '', (_analyticsData$overvi9 = analyticsData.overview) === null || _analyticsData$overvi9 === void 0 ? void 0 : _analyticsData$overvi9.durationChange, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Unique Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: clientData.uniqueUsers.toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${((_analyticsData$overvi0 = analyticsData.overview) === null || _analyticsData$overvi0 === void 0 ? void 0 : _analyticsData$overvi0.usersChange) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [((_analyticsData$overvi1 = analyticsData.overview) === null || _analyticsData$overvi1 === void 0 ? void 0 : _analyticsData$overvi1.usersChange) >= 0 ? '+' : '', (_analyticsData$overvi10 = analyticsData.overview) === null || _analyticsData$overvi10 === void 0 ? void 0 : _analyticsData$overvi10.usersChange, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.4\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-600\",\n                  children: \"Revenue Impact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-2xl font-semibold text-gray-900 mt-1\",\n                  children: [\"$\", clientData.revenue.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(ShoppingCart, {\n                  className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `text-sm font-medium ${((_analyticsData$overvi11 = analyticsData.overview) === null || _analyticsData$overvi11 === void 0 ? void 0 : _analyticsData$overvi11.revenueChange) >= 0 ? 'text-green-600' : 'text-red-600'}`,\n                children: [((_analyticsData$overvi12 = analyticsData.overview) === null || _analyticsData$overvi12 === void 0 ? void 0 : _analyticsData$overvi12.revenueChange) >= 0 ? '+' : '', (_analyticsData$overvi13 = analyticsData.overview) === null || _analyticsData$overvi13 === void 0 ? void 0 : _analyticsData$overvi13.revenueChange, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600 ml-2\",\n                children: \"from last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.5\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Try-On Trends\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64 md:h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: tryOnTrends,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"tryOns\",\n                    stroke: \"#2D8C88\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#2D8C88'\n                    },\n                    name: \"Try-Ons\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"conversions\",\n                    stroke: \"#10B981\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#10B981'\n                    },\n                    name: \"Conversions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6\n            },\n            className: \"bg-white rounded-xl shadow-sm p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Device Usage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-64 md:h-80\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: \"100%\",\n                children: /*#__PURE__*/_jsxDEV(PieChart, {\n                  children: [/*#__PURE__*/_jsxDEV(Pie, {\n                    data: deviceStats,\n                    cx: \"50%\",\n                    cy: \"50%\",\n                    labelLine: false,\n                    outerRadius: 80,\n                    fill: \"#8884d8\",\n                    dataKey: \"value\",\n                    label: ({\n                      name,\n                      percent\n                    }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                    children: deviceStats.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                      fill: entry.color\n                    }, `cell-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.7\n          },\n          className: \"bg-white rounded-xl shadow-sm p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Top Performing Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-64 md:h-80\",\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: productPerformance,\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"tryOns\",\n                  fill: \"#2D8C88\",\n                  name: \"Try-Ons\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"conversions\",\n                  fill: \"#10B981\",\n                  name: \"Conversions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.8\n          },\n          className: \"bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium\",\n                children: \"Ready to integrate Virtual Try-On?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#2D8C88]/80 mt-1\",\n                children: \"Add our try-on button to your product pages in minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors\",\n                children: \"View Guide\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors\",\n                children: \"Get Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl shadow-sm p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Recent Activity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [(_analyticsData$recent = analyticsData.recentActivity) === null || _analyticsData$recent === void 0 ? void 0 : _analyticsData$recent.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-10 h-10 rounded-full flex items-center justify-center ${activity.type === 'tryon' ? 'bg-[#2D8C88]/10' : activity.type === 'conversion' ? 'bg-green-500/10' : activity.type === 'user' ? 'bg-purple-500/10' : 'bg-blue-500/10'}`,\n                children: activity.type === 'tryon' ? /*#__PURE__*/_jsxDEV(Eye, {\n                  className: \"h-5 w-5 text-[#2D8C88]\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 50\n                }, this) : activity.type === 'conversion' ? /*#__PURE__*/_jsxDEV(ShoppingCart, {\n                  className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 55\n                }, this) : activity.type === 'user' ? /*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-5 w-5 text-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 49\n                }, this) : /*#__PURE__*/_jsxDEV(Globe, {\n                  className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: activity.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: activity.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: activity.timeAgo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)), (!analyticsData.recentActivity || analyticsData.recentActivity.length === 0) && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 text-center py-4\",\n              children: \"No recent activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EmbedCodeGenerator, {\n      isOpen: showEmbedModal,\n      onClose: () => setShowEmbedModal(false),\n      clientData: {\n        ...clientData,\n        productType: (user === null || user === void 0 ? void 0 : user.productType) || 'watches'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(ClientDashboard, \"6trvjI9da88C/rfIccxR48EBwBE=\");\n_c = ClientDashboard;\nexport default ClientDashboard;\nvar _c;\n$RefreshReg$(_c, \"ClientDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ClientSidebar", "ClientNavbar", "EmbedCodeGenerator", "motion", "Line<PERSON>hart", "Line", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "Eye", "TrendingUp", "Users", "ShoppingCart", "Clock", "Code", "Globe", "Smartphone", "axios", "jsxDEV", "_jsxDEV", "ClientDashboard", "_s", "_analyticsData$trends", "_analyticsData$produc", "_analyticsData$device", "_analyticsData$overvi", "_analyticsData$overvi2", "_analyticsData$overvi3", "_analyticsData$overvi4", "_analyticsData$overvi5", "_analyticsData$overvi6", "_analyticsData$overvi7", "_analyticsData$overvi8", "_analyticsData$overvi9", "_analyticsData$overvi0", "_analyticsData$overvi1", "_analyticsData$overvi10", "_analyticsData$overvi11", "_analyticsData$overvi12", "_analyticsData$overvi13", "_analyticsData$recent", "isSidebarOpen", "setIsSidebarOpen", "collapsed", "setCollapsed", "timeRange", "setTimeRange", "showEmbedModal", "setShowEmbedModal", "clientData", "setClientData", "id", "totalTryOns", "conversionRate", "avgDuration", "uniqueUsers", "revenue", "user", "setUser", "analyticsData", "setAnalyticsData", "trends", "products", "devices", "overview", "recentActivity", "loading", "setLoading", "toggleSidebar", "loadClientData", "token", "localStorage", "getItem", "Error", "profileResponse", "get", "process", "env", "REACT_APP_API_URL", "headers", "data", "prev", "companyName", "email", "analyticsResponse", "recentActivityResponse", "Promise", "all", "params", "error", "console", "<PERSON><PERSON><PERSON><PERSON>", "tryOnTrends", "map", "trend", "date", "_id", "tryOns", "sessions", "conversions", "productPerformance", "slice", "product", "name", "productName", "deviceStats", "device", "index", "value", "color", "className", "children", "isOpen", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "i", "range", "onClick", "div", "initial", "opacity", "y", "animate", "toLocaleString", "tryOnsChange", "transition", "delay", "conversionChange", "Math", "floor", "durationChange", "usersChange", "revenueChange", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "type", "stroke", "strokeWidth", "dot", "fill", "cx", "cy", "labelLine", "outerRadius", "label", "percent", "toFixed", "entry", "activity", "title", "description", "timeAgo", "length", "productType", "_c", "$RefreshReg$"], "sources": ["D:/Via/New folder/v2/src/pages/client/ClientDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Client<PERSON>idebar from '../../components/client/ClientSidebar';\r\nimport ClientNavbar from '../../components/client/ClientNavbar';\r\nimport EmbedCodeGenerator from '../../components/EmbedCodeGenerator';\r\nimport { motion } from 'framer-motion';\r\nimport { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';\r\nimport { Eye, TrendingUp, Users, ShoppingCart, Clock, Code, Globe, Smartphone } from 'lucide-react';\r\nimport axios from 'axios';\r\n\r\nconst ClientDashboard = () => {\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [timeRange, setTimeRange] = useState('7d');\r\n  const [showEmbedModal, setShowEmbedModal] = useState(false);\r\n  const [clientData, setClientData] = useState({\r\n    id: '',\r\n    totalTryOns: 0,\r\n    conversionRate: 0,\r\n    avgDuration: 0,\r\n    uniqueUsers: 0,\r\n    revenue: 0\r\n  });\r\n  const [user, setUser] = useState(null);\r\n  const [analyticsData, setAnalyticsData] = useState({\r\n    trends: [],\r\n    products: [],\r\n    devices: [],\r\n    overview: null,\r\n    recentActivity: []\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const toggleSidebar = () => {\r\n    setIsSidebarOpen(!isSidebarOpen);\r\n  };\r\n\r\n  // Load client data from localStorage and API\r\n  useEffect(() => {\r\n    const loadClientData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const token = localStorage.getItem('token');\r\n\r\n        if (!token) {\r\n          throw new Error('No authentication token found');\r\n        }\r\n\r\n        // Fetch client profile\r\n        const profileResponse = await axios.get(`${process.env.REACT_APP_API_URL}/api/auth/me`, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json'\r\n          }\r\n        });\r\n\r\n        if (profileResponse.data) {\r\n          setClientData(prev => ({\r\n            ...prev,\r\n            companyName: profileResponse.data.companyName || '',\r\n            email: profileResponse.data.email || ''\r\n          }));\r\n        }\r\n\r\n        // Fetch analytics data\r\n        const [analyticsResponse, recentActivityResponse] = await Promise.all([\r\n          axios.get(`${process.env.REACT_APP_API_URL}/api/analytics/client/overview`, {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            },\r\n            params: { timeRange }\r\n          }),\r\n          axios.get(`${process.env.REACT_APP_API_URL}/api/analytics/client/recent-activity`, {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json'\r\n            },\r\n            params: { timeRange }\r\n          })\r\n        ]);\r\n\r\n        if (analyticsResponse.data) {\r\n          setAnalyticsData(analyticsResponse.data);\r\n        }\r\n\r\n        if (recentActivityResponse.data) {\r\n          setAnalyticsData(prev => ({\r\n            ...prev,\r\n            recentActivity: recentActivityResponse.data\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading client data:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadClientData();\r\n  }, [timeRange]);\r\n\r\n  // Calculate margin for main content\r\n  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';\r\n\r\n  // Format data for charts\r\n  const tryOnTrends = analyticsData.trends?.map(trend => ({\r\n    date: trend._id,\r\n    tryOns: trend.sessions,\r\n    conversions: trend.conversions\r\n  })) || [];\r\n\r\n  const productPerformance = analyticsData.products?.slice(0, 5).map(product => ({\r\n    name: product.productName || 'Unknown Product',\r\n    tryOns: product.sessions,\r\n    conversions: product.conversions\r\n  })) || [];\r\n\r\n  const deviceStats = analyticsData.devices?.map((device, index) => ({\r\n    name: device._id,\r\n    value: device.sessions,\r\n    color: ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index % 5]\r\n  })) || [];\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\r\n        <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\r\n\r\n        <main className={`${mainMargin} pt-20 transition-all duration-300`}>\r\n          <div className=\"p-4 md:p-6 space-y-6\">\r\n            <div className=\"animate-pulse\">\r\n              <div className=\"h-8 bg-gray-200 rounded w-1/3 mb-2\"></div>\r\n              <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-6\"></div>\r\n\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-4 md:gap-6 mb-6\">\r\n                {[1, 2, 3, 4, 5].map((i) => (\r\n                  <div key={i} className=\"bg-white rounded-xl shadow-sm p-6\">\r\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\r\n                    <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-4\"></div>\r\n                    <div className=\"h-4 bg-gray-200 rounded w-1/4\"></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\">\r\n                {[1, 2].map((i) => (\r\n                  <div key={i} className=\"bg-white rounded-xl shadow-sm p-6\">\r\n                    <div className=\"h-6 bg-gray-200 rounded w-1/4 mb-4\"></div>\r\n                    <div className=\"h-80 bg-gray-200 rounded\"></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <ClientSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />\r\n      <ClientNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />\r\n\r\n      {/* Main Content */}\r\n      <main className={`${mainMargin} pt-20 transition-all duration-300`}>\r\n        <div className=\"p-4 md:p-6 space-y-6\">\r\n          {/* Page Header */}\r\n          <div className=\"mb-6 flex flex-col md:flex-row md:items-center md:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold text-gray-900\">Virtual Try-On Dashboard</h1>\r\n              <p className=\"text-gray-600\">Monitor your product performance and customer engagement</p>\r\n            </div>\r\n            <div className=\"mt-4 md:mt-0 flex space-x-3\">\r\n              <div className=\"inline-flex rounded-lg border border-gray-200 p-1\">\r\n                {['7d', '30d', '90d', '1y'].map((range) => (\r\n                  <button\r\n                    key={range}\r\n                    onClick={() => setTimeRange(range)}\r\n                    className={`px-3 py-1 text-sm font-medium rounded-md ${\r\n                      timeRange === range\r\n                        ? 'bg-[#2D8C88] text-white'\r\n                        : 'text-gray-600 hover:text-gray-900'\r\n                    }`}\r\n                  >\r\n                    {range}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              <button\r\n                onClick={() => setShowEmbedModal(true)}\r\n                className=\"inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2\"\r\n              >\r\n                <Code className=\"h-4 w-4 mr-2\" />\r\n                Get Embed Code\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Enhanced Stats Grid */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-5 gap-4 md:gap-6\">\r\n            {/* Total Try-Ons */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Total Try-Ons</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.totalTryOns.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center\">\r\n                  <Eye className=\"h-6 w-6 text-[#2D8C88]\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className={`text-sm font-medium ${analyticsData.overview?.tryOnsChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                  {analyticsData.overview?.tryOnsChange >= 0 ? '+' : ''}{analyticsData.overview?.tryOnsChange}%\r\n                </span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last week</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Conversion Rate */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.1 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Conversion Rate</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.conversionRate}%</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\r\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className={`text-sm font-medium ${analyticsData.overview?.conversionChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                  {analyticsData.overview?.conversionChange >= 0 ? '+' : ''}{analyticsData.overview?.conversionChange}%\r\n                </span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Average Duration */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Avg Duration</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{Math.floor(clientData.avgDuration / 60)}m {clientData.avgDuration % 60}s</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center\">\r\n                  <Clock className=\"h-6 w-6 text-blue-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className={`text-sm font-medium ${analyticsData.overview?.durationChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                  {analyticsData.overview?.durationChange >= 0 ? '+' : ''}{analyticsData.overview?.durationChange}s\r\n                </span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Unique Users */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.3 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Unique Users</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">{clientData.uniqueUsers.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center\">\r\n                  <Users className=\"h-6 w-6 text-purple-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className={`text-sm font-medium ${analyticsData.overview?.usersChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                  {analyticsData.overview?.usersChange >= 0 ? '+' : ''}{analyticsData.overview?.usersChange}%\r\n                </span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last week</span>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Revenue Impact */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.4 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-600\">Revenue Impact</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 mt-1\">${clientData.revenue.toLocaleString()}</p>\r\n                </div>\r\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center\">\r\n                  <ShoppingCart className=\"h-6 w-6 text-green-500\" />\r\n                </div>\r\n              </div>\r\n              <div className=\"mt-4\">\r\n                <span className={`text-sm font-medium ${analyticsData.overview?.revenueChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\r\n                  {analyticsData.overview?.revenueChange >= 0 ? '+' : ''}{analyticsData.overview?.revenueChange}%\r\n                </span>\r\n                <span className=\"text-sm text-gray-600 ml-2\">from last month</span>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Charts Grid */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\">\r\n            {/* Try-On Trends */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.5 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Try-On Trends</h3>\r\n              <div className=\"h-64 md:h-80\">\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <LineChart data={tryOnTrends}>\r\n                    <CartesianGrid strokeDasharray=\"3 3\" />\r\n                    <XAxis dataKey=\"date\" />\r\n                    <YAxis />\r\n                    <Tooltip />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"tryOns\"\r\n                      stroke=\"#2D8C88\"\r\n                      strokeWidth={2}\r\n                      dot={{ fill: '#2D8C88' }}\r\n                      name=\"Try-Ons\"\r\n                    />\r\n                    <Line\r\n                      type=\"monotone\"\r\n                      dataKey=\"conversions\"\r\n                      stroke=\"#10B981\"\r\n                      strokeWidth={2}\r\n                      dot={{ fill: '#10B981' }}\r\n                      name=\"Conversions\"\r\n                    />\r\n                  </LineChart>\r\n                </ResponsiveContainer>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Device Distribution */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.6 }}\r\n              className=\"bg-white rounded-xl shadow-sm p-6\"\r\n            >\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Device Usage</h3>\r\n              <div className=\"h-64 md:h-80\">\r\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                  <PieChart>\r\n                    <Pie\r\n                      data={deviceStats}\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      labelLine={false}\r\n                      outerRadius={80}\r\n                      fill=\"#8884d8\"\r\n                      dataKey=\"value\"\r\n                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\r\n                    >\r\n                      {deviceStats.map((entry, index) => (\r\n                        <Cell key={`cell-${index}`} fill={entry.color} />\r\n                      ))}\r\n                    </Pie>\r\n                    <Tooltip />\r\n                  </PieChart>\r\n                </ResponsiveContainer>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Product Performance */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.7 }}\r\n            className=\"bg-white rounded-xl shadow-sm p-6\"\r\n          >\r\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Top Performing Products</h3>\r\n            <div className=\"h-64 md:h-80\">\r\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n                <BarChart data={productPerformance}>\r\n                  <CartesianGrid strokeDasharray=\"3 3\" />\r\n                  <XAxis dataKey=\"name\" />\r\n                  <YAxis />\r\n                  <Tooltip />\r\n                  <Bar dataKey=\"tryOns\" fill=\"#2D8C88\" name=\"Try-Ons\" />\r\n                  <Bar dataKey=\"conversions\" fill=\"#10B981\" name=\"Conversions\" />\r\n                </BarChart>\r\n              </ResponsiveContainer>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Integration Guide */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.8 }}\r\n            className=\"bg-gradient-to-r from-[#2D8C88] to-[#236b68] rounded-xl shadow-sm p-6\"\r\n          >\r\n            <div className=\"flex items-center justify-between text-white\">\r\n              <div>\r\n                <h3 className=\"text-lg font-medium\">Ready to integrate Virtual Try-On?</h3>\r\n                <p className=\"text-[#2D8C88]/80 mt-1\">Add our try-on button to your product pages in minutes</p>\r\n              </div>\r\n              <div className=\"flex space-x-3\">\r\n                <button className=\"bg-white text-[#2D8C88] px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors\">\r\n                  View Guide\r\n                </button>\r\n                <button className=\"bg-[#236b68] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1e5a57] transition-colors\">\r\n                  Get Code\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Recent Activity */}\r\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\r\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h2>\r\n            <div className=\"space-y-4\">\r\n              {analyticsData.recentActivity?.map((activity, index) => (\r\n                <div key={index} className=\"flex items-start space-x-4\">\r\n                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${\r\n                    activity.type === 'tryon' ? 'bg-[#2D8C88]/10' :\r\n                    activity.type === 'conversion' ? 'bg-green-500/10' :\r\n                    activity.type === 'user' ? 'bg-purple-500/10' :\r\n                    'bg-blue-500/10'\r\n                  }`}>\r\n                    {activity.type === 'tryon' ? <Eye className=\"h-5 w-5 text-[#2D8C88]\" /> :\r\n                     activity.type === 'conversion' ? <ShoppingCart className=\"h-5 w-5 text-green-500\" /> :\r\n                     activity.type === 'user' ? <Users className=\"h-5 w-5 text-purple-500\" /> :\r\n                     <Globe className=\"h-5 w-5 text-blue-500\" />}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\r\n                    <p className=\"text-sm text-gray-600\">{activity.description}</p>\r\n                    <p className=\"text-xs text-gray-500 mt-1\">{activity.timeAgo}</p>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n              {(!analyticsData.recentActivity || analyticsData.recentActivity.length === 0) && (\r\n                <p className=\"text-sm text-gray-500 text-center py-4\">No recent activity</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n\r\n      {/* Embed Code Modal */}\r\n      <EmbedCodeGenerator\r\n        isOpen={showEmbedModal}\r\n        onClose={() => setShowEmbedModal(false)}\r\n        clientData={{...clientData, productType: user?.productType || 'watches'}}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ClientDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,QAAQ,UAAU;AACzI,SAASC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,YAAY,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,cAAc;AACnG,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,qBAAA;EAC5B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC;IAC3C4D,EAAE,EAAE,EAAE;IACNC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC;IACjDsE,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM6E,aAAa,GAAGA,CAAA,KAAM;IAC1B1B,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACd,MAAM6E,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFF,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;QAClD;;QAEA;QACA,MAAMC,eAAe,GAAG,MAAMzD,KAAK,CAAC0D,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,cAAc,EAAE;UACtFC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAII,eAAe,CAACM,IAAI,EAAE;UACxB9B,aAAa,CAAC+B,IAAI,KAAK;YACrB,GAAGA,IAAI;YACPC,WAAW,EAAER,eAAe,CAACM,IAAI,CAACE,WAAW,IAAI,EAAE;YACnDC,KAAK,EAAET,eAAe,CAACM,IAAI,CAACG,KAAK,IAAI;UACvC,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,MAAM,CAACC,iBAAiB,EAAEC,sBAAsB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpEtE,KAAK,CAAC0D,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,gCAAgC,EAAE;UAC1EC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB,CAAC;UACDkB,MAAM,EAAE;YAAE3C;UAAU;QACtB,CAAC,CAAC,EACF5B,KAAK,CAAC0D,GAAG,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,uCAAuC,EAAE;UACjFC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB,CAAC;UACDkB,MAAM,EAAE;YAAE3C;UAAU;QACtB,CAAC,CAAC,CACH,CAAC;QAEF,IAAIuC,iBAAiB,CAACJ,IAAI,EAAE;UAC1BpB,gBAAgB,CAACwB,iBAAiB,CAACJ,IAAI,CAAC;QAC1C;QAEA,IAAIK,sBAAsB,CAACL,IAAI,EAAE;UAC/BpB,gBAAgB,CAACqB,IAAI,KAAK;YACxB,GAAGA,IAAI;YACPhB,cAAc,EAAEoB,sBAAsB,CAACL;UACzC,CAAC,CAAC,CAAC;QACL;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACxB,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM8C,UAAU,GAAGhD,SAAS,GAAG,cAAc,GAAG,eAAe;;EAE/D;EACA,MAAMiD,WAAW,GAAG,EAAAtE,qBAAA,GAAAqC,aAAa,CAACE,MAAM,cAAAvC,qBAAA,uBAApBA,qBAAA,CAAsBuE,GAAG,CAACC,KAAK,KAAK;IACtDC,IAAI,EAAED,KAAK,CAACE,GAAG;IACfC,MAAM,EAAEH,KAAK,CAACI,QAAQ;IACtBC,WAAW,EAAEL,KAAK,CAACK;EACrB,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMC,kBAAkB,GAAG,EAAA7E,qBAAA,GAAAoC,aAAa,CAACG,QAAQ,cAAAvC,qBAAA,uBAAtBA,qBAAA,CAAwB8E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACR,GAAG,CAACS,OAAO,KAAK;IAC7EC,IAAI,EAAED,OAAO,CAACE,WAAW,IAAI,iBAAiB;IAC9CP,MAAM,EAAEK,OAAO,CAACJ,QAAQ;IACxBC,WAAW,EAAEG,OAAO,CAACH;EACvB,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMM,WAAW,GAAG,EAAAjF,qBAAA,GAAAmC,aAAa,CAACI,OAAO,cAAAvC,qBAAA,uBAArBA,qBAAA,CAAuBqE,GAAG,CAAC,CAACa,MAAM,EAAEC,KAAK,MAAM;IACjEJ,IAAI,EAAEG,MAAM,CAACV,GAAG;IAChBY,KAAK,EAAEF,MAAM,CAACR,QAAQ;IACtBW,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACF,KAAK,GAAG,CAAC;EAC1E,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,IAAIzC,OAAO,EAAE;IACX,oBACE/C,OAAA;MAAK2F,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC5F,OAAA,CAAC1B,aAAa;QAACuH,MAAM,EAAEvE,aAAc;QAACwE,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAAC,KAAK,CAAE;QAACC,SAAS,EAAEA,SAAU;QAACC,YAAY,EAAEA;MAAa;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClIlG,OAAA,CAACzB,YAAY;QAAC0E,aAAa,EAAEA,aAAc;QAACzB,SAAS,EAAEA;MAAU;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpElG,OAAA;QAAM2F,SAAS,EAAE,GAAGnB,UAAU,oCAAqC;QAAAoB,QAAA,eACjE5F,OAAA;UAAK2F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnC5F,OAAA;YAAK2F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5F,OAAA;cAAK2F,SAAS,EAAC;YAAoC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DlG,OAAA;cAAK2F,SAAS,EAAC;YAAoC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE1DlG,OAAA;cAAK2F,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAChF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAClB,GAAG,CAAEyB,CAAC,iBACrBnG,OAAA;gBAAa2F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACxD5F,OAAA;kBAAK2F,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DlG,OAAA;kBAAK2F,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DlG,OAAA;kBAAK2F,SAAS,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAH7CC,CAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlG,OAAA;cAAK2F,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAC5D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAClB,GAAG,CAAEyB,CAAC,iBACZnG,OAAA;gBAAa2F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACxD5F,OAAA;kBAAK2F,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DlG,OAAA;kBAAK2F,SAAS,EAAC;gBAA0B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GAFxCC,CAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGN,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACElG,OAAA;IAAK2F,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC5F,OAAA,CAAC1B,aAAa;MAACuH,MAAM,EAAEvE,aAAc;MAACwE,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAAC,KAAK,CAAE;MAACC,SAAS,EAAEA,SAAU;MAACC,YAAY,EAAEA;IAAa;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClIlG,OAAA,CAACzB,YAAY;MAAC0E,aAAa,EAAEA,aAAc;MAACzB,SAAS,EAAEA;IAAU;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpElG,OAAA;MAAM2F,SAAS,EAAE,GAAGnB,UAAU,oCAAqC;MAAAoB,QAAA,eACjE5F,OAAA;QAAK2F,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEnC5F,OAAA;UAAK2F,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChF5F,OAAA;YAAA4F,QAAA,gBACE5F,OAAA;cAAI2F,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ElG,OAAA;cAAG2F,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNlG,OAAA;YAAK2F,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C5F,OAAA;cAAK2F,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC/D,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAClB,GAAG,CAAE0B,KAAK,iBACpCpG,OAAA;gBAEEqG,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAACyE,KAAK,CAAE;gBACnCT,SAAS,EAAE,4CACTjE,SAAS,KAAK0E,KAAK,GACf,yBAAyB,GACzB,mCAAmC,EACtC;gBAAAR,QAAA,EAEFQ;cAAK,GARDA,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlG,OAAA;cACEqG,OAAO,EAAEA,CAAA,KAAMxE,iBAAiB,CAAC,IAAI,CAAE;cACvC8D,SAAS,EAAC,6KAA6K;cAAAC,QAAA,gBAEvL5F,OAAA,CAACL,IAAI;gBAACgG,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlG,OAAA;UAAK2F,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAE5E5F,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9Bd,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAK2F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5F,OAAA;gBAAA4F,QAAA,gBACE5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClElG,OAAA;kBAAG2F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE9D,UAAU,CAACG,WAAW,CAAC0E,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlG,OAAA;gBAAK2F,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5F,OAAA,CAACV,GAAG;kBAACqG,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5F,OAAA;gBAAM2F,SAAS,EAAE,uBAAuB,EAAArF,qBAAA,GAAAkC,aAAa,CAACK,QAAQ,cAAAvC,qBAAA,uBAAtBA,qBAAA,CAAwBsG,YAAY,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAhB,QAAA,GACrH,EAAArF,sBAAA,GAAAiC,aAAa,CAACK,QAAQ,cAAAtC,sBAAA,uBAAtBA,sBAAA,CAAwBqG,YAAY,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAApG,sBAAA,GAAEgC,aAAa,CAACK,QAAQ,cAAArC,sBAAA,uBAAtBA,sBAAA,CAAwBoG,YAAY,EAAC,GAC9F;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlG,OAAA;gBAAM2F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BnB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAK2F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5F,OAAA;gBAAA4F,QAAA,gBACE5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpElG,OAAA;kBAAG2F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAE9D,UAAU,CAACI,cAAc,EAAC,GAAC;gBAAA;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNlG,OAAA;gBAAK2F,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5F,OAAA,CAACT,UAAU;kBAACoG,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5F,OAAA;gBAAM2F,SAAS,EAAE,uBAAuB,EAAAlF,sBAAA,GAAA+B,aAAa,CAACK,QAAQ,cAAApC,sBAAA,uBAAtBA,sBAAA,CAAwBsG,gBAAgB,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAnB,QAAA,GACzH,EAAAlF,sBAAA,GAAA8B,aAAa,CAACK,QAAQ,cAAAnC,sBAAA,uBAAtBA,sBAAA,CAAwBqG,gBAAgB,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAApG,sBAAA,GAAE6B,aAAa,CAACK,QAAQ,cAAAlC,sBAAA,uBAAtBA,sBAAA,CAAwBoG,gBAAgB,EAAC,GACtG;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlG,OAAA;gBAAM2F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BnB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAK2F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5F,OAAA;gBAAA4F,QAAA,gBACE5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjElG,OAAA;kBAAG2F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAEoB,IAAI,CAACC,KAAK,CAACnF,UAAU,CAACK,WAAW,GAAG,EAAE,CAAC,EAAC,IAAE,EAACL,UAAU,CAACK,WAAW,GAAG,EAAE,EAAC,GAAC;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnI,CAAC,eACNlG,OAAA;gBAAK2F,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,eACrF5F,OAAA,CAACN,KAAK;kBAACiG,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5F,OAAA;gBAAM2F,SAAS,EAAE,uBAAuB,EAAA/E,sBAAA,GAAA4B,aAAa,CAACK,QAAQ,cAAAjC,sBAAA,uBAAtBA,sBAAA,CAAwBsG,cAAc,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAtB,QAAA,GACvH,EAAA/E,sBAAA,GAAA2B,aAAa,CAACK,QAAQ,cAAAhC,sBAAA,uBAAtBA,sBAAA,CAAwBqG,cAAc,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAApG,sBAAA,GAAE0B,aAAa,CAACK,QAAQ,cAAA/B,sBAAA,uBAAtBA,sBAAA,CAAwBoG,cAAc,EAAC,GAClG;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlG,OAAA;gBAAM2F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BnB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAK2F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5F,OAAA;gBAAA4F,QAAA,gBACE5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjElG,OAAA;kBAAG2F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAE9D,UAAU,CAACM,WAAW,CAACuE,cAAc,CAAC;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlG,OAAA;gBAAK2F,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,eACvF5F,OAAA,CAACR,KAAK;kBAACmG,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5F,OAAA;gBAAM2F,SAAS,EAAE,uBAAuB,EAAA5E,sBAAA,GAAAyB,aAAa,CAACK,QAAQ,cAAA9B,sBAAA,uBAAtBA,sBAAA,CAAwBoG,WAAW,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAvB,QAAA,GACpH,EAAA5E,sBAAA,GAAAwB,aAAa,CAACK,QAAQ,cAAA7B,sBAAA,uBAAtBA,sBAAA,CAAwBmG,WAAW,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAAlG,uBAAA,GAAEuB,aAAa,CAACK,QAAQ,cAAA5B,uBAAA,uBAAtBA,uBAAA,CAAwBkG,WAAW,EAAC,GAC5F;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlG,OAAA;gBAAM2F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BnB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAK2F,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5F,OAAA;gBAAA4F,QAAA,gBACE5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnElG,OAAA;kBAAG2F,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,GAAC,GAAC,EAAC9D,UAAU,CAACO,OAAO,CAACsE,cAAc,CAAC,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC,eACNlG,OAAA;gBAAK2F,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtF5F,OAAA,CAACP,YAAY;kBAACkG,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlG,OAAA;cAAK2F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5F,OAAA;gBAAM2F,SAAS,EAAE,uBAAuB,EAAAzE,uBAAA,GAAAsB,aAAa,CAACK,QAAQ,cAAA3B,uBAAA,uBAAtBA,uBAAA,CAAwBkG,aAAa,KAAI,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAAG;gBAAAxB,QAAA,GACtH,EAAAzE,uBAAA,GAAAqB,aAAa,CAACK,QAAQ,cAAA1B,uBAAA,uBAAtBA,uBAAA,CAAwBiG,aAAa,KAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAAhG,uBAAA,GAAEoB,aAAa,CAACK,QAAQ,cAAAzB,uBAAA,uBAAtBA,uBAAA,CAAwBgG,aAAa,EAAC,GAChG;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPlG,OAAA;gBAAM2F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlG,OAAA;UAAK2F,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAE7D5F,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BnB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAI2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzElG,OAAA;cAAK2F,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5F,OAAA,CAACd,mBAAmB;gBAACmI,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAA1B,QAAA,eAC7C5F,OAAA,CAACtB,SAAS;kBAACmF,IAAI,EAAEY,WAAY;kBAAAmB,QAAA,gBAC3B5F,OAAA,CAAChB,aAAa;oBAACuI,eAAe,EAAC;kBAAK;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvClG,OAAA,CAAClB,KAAK;oBAAC0I,OAAO,EAAC;kBAAM;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBlG,OAAA,CAACjB,KAAK;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACTlG,OAAA,CAACf,OAAO;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACXlG,OAAA,CAACrB,IAAI;oBACH8I,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,QAAQ;oBAChBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBzC,IAAI,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACFlG,OAAA,CAACrB,IAAI;oBACH8I,IAAI,EAAC,UAAU;oBACfD,OAAO,EAAC,aAAa;oBACrBE,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE;oBAAU,CAAE;oBACzBzC,IAAI,EAAC;kBAAa;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGblG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3BnB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAE7C5F,OAAA;cAAI2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxElG,OAAA;cAAK2F,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5F,OAAA,CAACd,mBAAmB;gBAACmI,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAC,MAAM;gBAAA1B,QAAA,eAC7C5F,OAAA,CAACb,QAAQ;kBAAAyG,QAAA,gBACP5F,OAAA,CAACZ,GAAG;oBACFyE,IAAI,EAAEyB,WAAY;oBAClBwC,EAAE,EAAC,KAAK;oBACRC,EAAE,EAAC,KAAK;oBACRC,SAAS,EAAE,KAAM;oBACjBC,WAAW,EAAE,EAAG;oBAChBJ,IAAI,EAAC,SAAS;oBACdL,OAAO,EAAC,OAAO;oBACfU,KAAK,EAAEA,CAAC;sBAAE9C,IAAI;sBAAE+C;oBAAQ,CAAC,KAAK,GAAG/C,IAAI,IAAI,CAAC+C,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;oBAAAxC,QAAA,EAEtEN,WAAW,CAACZ,GAAG,CAAC,CAAC2D,KAAK,EAAE7C,KAAK,kBAC5BxF,OAAA,CAACX,IAAI;sBAAuBwI,IAAI,EAAEQ,KAAK,CAAC3C;oBAAM,GAAnC,QAAQF,KAAK,EAAE;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAsB,CACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNlG,OAAA,CAACf,OAAO;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNlG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BnB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAE7C5F,OAAA;YAAI2F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAuB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFlG,OAAA;YAAK2F,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B5F,OAAA,CAACd,mBAAmB;cAACmI,KAAK,EAAC,MAAM;cAACC,MAAM,EAAC,MAAM;cAAA1B,QAAA,eAC7C5F,OAAA,CAACpB,QAAQ;gBAACiF,IAAI,EAAEoB,kBAAmB;gBAAAW,QAAA,gBACjC5F,OAAA,CAAChB,aAAa;kBAACuI,eAAe,EAAC;gBAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvClG,OAAA,CAAClB,KAAK;kBAAC0I,OAAO,EAAC;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxBlG,OAAA,CAACjB,KAAK;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTlG,OAAA,CAACf,OAAO;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXlG,OAAA,CAACnB,GAAG;kBAAC2I,OAAO,EAAC,QAAQ;kBAACK,IAAI,EAAC,SAAS;kBAACzC,IAAI,EAAC;gBAAS;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtDlG,OAAA,CAACnB,GAAG;kBAAC2I,OAAO,EAAC,aAAa;kBAACK,IAAI,EAAC,SAAS;kBAACzC,IAAI,EAAC;gBAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGblG,OAAA,CAACvB,MAAM,CAAC6H,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BI,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BnB,SAAS,EAAC,uEAAuE;UAAAC,QAAA,eAEjF5F,OAAA;YAAK2F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAC3D5F,OAAA;cAAA4F,QAAA,gBACE5F,OAAA;gBAAI2F,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ElG,OAAA;gBAAG2F,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACNlG,OAAA;cAAK2F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5F,OAAA;gBAAQ2F,SAAS,EAAC,6FAA6F;gBAAAC,QAAA,EAAC;cAEhH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlG,OAAA;gBAAQ2F,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAAC;cAElH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGblG,OAAA;UAAK2F,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5F,OAAA;YAAI2F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ElG,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAC,QAAA,IAAAvE,qBAAA,GACvBmB,aAAa,CAACM,cAAc,cAAAzB,qBAAA,uBAA5BA,qBAAA,CAA8BqD,GAAG,CAAC,CAAC4D,QAAQ,EAAE9C,KAAK,kBACjDxF,OAAA;cAAiB2F,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACrD5F,OAAA;gBAAK2F,SAAS,EAAE,2DACd2C,QAAQ,CAACb,IAAI,KAAK,OAAO,GAAG,iBAAiB,GAC7Ca,QAAQ,CAACb,IAAI,KAAK,YAAY,GAAG,iBAAiB,GAClDa,QAAQ,CAACb,IAAI,KAAK,MAAM,GAAG,kBAAkB,GAC7C,gBAAgB,EACf;gBAAA7B,QAAA,EACA0C,QAAQ,CAACb,IAAI,KAAK,OAAO,gBAAGzH,OAAA,CAACV,GAAG;kBAACqG,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GACtEoC,QAAQ,CAACb,IAAI,KAAK,YAAY,gBAAGzH,OAAA,CAACP,YAAY;kBAACkG,SAAS,EAAC;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GACpFoC,QAAQ,CAACb,IAAI,KAAK,MAAM,gBAAGzH,OAAA,CAACR,KAAK;kBAACmG,SAAS,EAAC;gBAAyB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACxElG,OAAA,CAACJ,KAAK;kBAAC+F,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNlG,OAAA;gBAAK2F,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB5F,OAAA;kBAAG2F,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE0C,QAAQ,CAACC;gBAAK;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrElG,OAAA;kBAAG2F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAE0C,QAAQ,CAACE;gBAAW;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/DlG,OAAA;kBAAG2F,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAE0C,QAAQ,CAACG;gBAAO;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAhBEV,KAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBV,CACN,CAAC,EACD,CAAC,CAAC1D,aAAa,CAACM,cAAc,IAAIN,aAAa,CAACM,cAAc,CAAC4F,MAAM,KAAK,CAAC,kBAC1E1I,OAAA;cAAG2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC5E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPlG,OAAA,CAACxB,kBAAkB;MACjBqH,MAAM,EAAEjE,cAAe;MACvBkE,OAAO,EAAEA,CAAA,KAAMjE,iBAAiB,CAAC,KAAK,CAAE;MACxCC,UAAU,EAAE;QAAC,GAAGA,UAAU;QAAE6G,WAAW,EAAE,CAAArG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqG,WAAW,KAAI;MAAS;IAAE;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAChG,EAAA,CApdID,eAAe;AAAA2I,EAAA,GAAf3I,eAAe;AAsdrB,eAAeA,eAAe;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}